#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻智能处理主控制脚本
整合新闻爬取、话题合并、话题分析的完整流程
"""

import json
import sys
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 导入各个模块
from news_crawler import NewsCrawler, NewsFormatter
from topic_merger import TopicMerger
from topic_processor import TopicProcessor
from llm_client import LLMClient


class NewsIntelligenceSystem:
    """新闻智能处理系统"""
    
    def __init__(self):
        """初始化系统"""
        self.crawler = NewsCrawler()
        self.formatter = NewsFormatter()
        self.merger = TopicMerger()
        self.processor = TopicProcessor()
        
        print("🚀 新闻智能处理系统初始化完成")
    
    def run_full_pipeline(self, save_intermediate: bool = True) -> Dict[str, str]:
        """
        运行完整的处理流程

        Args:
            save_intermediate: 是否保存中间结果

        Returns:
            各阶段输出文件路径
        """
        print("🎯 开始执行完整的新闻智能处理流程")
        print("=" * 70)
        
        results = {}
        
        try:
            # 第一步：爬取新闻数据
            print("\n📡 第一步：爬取各平台热门话题")
            print("-" * 50)
            
            news_data = self.crawler.get_all_news()
            
            if save_intermediate:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                raw_file = self.crawler.save_to_file(news_data, f"news_data_{timestamp}.json")
                results['raw_data'] = raw_file
                print(f"💾 原始数据已保存: {raw_file}")
            
            # 第二步：格式化标题
            print("\n🔄 第二步：格式化新闻标题")
            print("-" * 50)
            
            json_file, txt_file = self.formatter.format_news_data(news_data)
            results['formatted_json'] = str(json_file)
            results['formatted_txt'] = str(txt_file)
            
            # 加载格式化后的数据
            with open(json_file, 'r', encoding='utf-8') as f:
                formatted_data = json.load(f)
            
            # 第三步：合并相似话题
            print("\n🔀 第三步：合并相似话题")
            print("-" * 50)
            
            merged_topics = self.merger.merge_topics(formatted_data)
            
            if save_intermediate:
                merged_file = self.merger.save_merged_topics(merged_topics)
                results['merged_topics'] = merged_file
            
            self.merger.print_merged_summary(merged_topics)
            
            # 第四步：分析处理话题
            print("\n🔍 第四步：深度分析话题")
            print("-" * 50)
            
            processed_topics = self.processor.process_all_topics(merged_topics)
            
            processed_file = self.processor.save_processed_topics(processed_topics)
            results['processed_topics'] = processed_file
            
            self.processor.print_processing_summary(processed_topics)
            
            # 生成最终报告
            print("\n📊 第五步：生成处理报告")
            print("-" * 50)
            
            report_file = self._generate_final_report(results, processed_topics)
            results['final_report'] = report_file
            
            print("\n" + "=" * 70)
            print("🎉 新闻智能处理流程完成！")
            print("=" * 70)
            
            return results
            
        except Exception as e:
            print(f"\n❌ 处理流程失败: {e}")
            raise e
    
    def run_from_formatted_data(self, formatted_file: str) -> Dict[str, str]:
        """
        从格式化数据开始运行流程

        Args:
            formatted_file: 格式化标题文件路径

        Returns:
            输出文件路径
        """
        print(f"🎯 从格式化数据开始处理: {formatted_file}")
        print("=" * 70)
        
        results = {'formatted_json': formatted_file}
        
        try:
            # 加载格式化数据
            with open(formatted_file, 'r', encoding='utf-8') as f:
                formatted_data = json.load(f)
            
            # 合并话题
            print("\n🔀 第一步：合并相似话题")
            print("-" * 50)
            
            merged_topics = self.merger.merge_topics(formatted_data)
            merged_file = self.merger.save_merged_topics(merged_topics)
            results['merged_topics'] = merged_file
            
            self.merger.print_merged_summary(merged_topics)
            
            # 分析话题
            print("\n🔍 第二步：深度分析话题")
            print("-" * 50)
            
            processed_topics = self.processor.process_all_topics(merged_topics)
            processed_file = self.processor.save_processed_topics(processed_topics)
            results['processed_topics'] = processed_file
            
            self.processor.print_processing_summary(processed_topics)
            
            # 生成报告
            print("\n📊 第三步：生成处理报告")
            print("-" * 50)
            
            report_file = self._generate_final_report(results, processed_topics)
            results['final_report'] = report_file
            
            print("\n" + "=" * 70)
            print("🎉 处理完成！")
            print("=" * 70)
            
            return results
            
        except Exception as e:
            print(f"\n❌ 处理失败: {e}")
            raise e
    
    def _generate_final_report(self, results: Dict[str, str], 
                              processed_topics: List[Dict[str, Any]]) -> str:
        """生成最终处理报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = Path("news_output") / f"intelligence_report_{timestamp}.md"
        
        # 统计信息
        total_topics = len(processed_topics)
        successful_topics = sum(1 for t in processed_topics if 'error' not in t)
        error_topics = total_topics - successful_topics
        
        # 生成报告内容
        report_content = f"""# 新闻智能处理报告

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 处理统计

- **总话题数**: {total_topics}
- **成功处理**: {successful_topics}
- **处理失败**: {error_topics}
- **成功率**: {successful_topics/total_topics*100:.1f}%

## 📁 输出文件

"""
        
        for key, file_path in results.items():
            if file_path:
                report_content += f"- **{key}**: `{file_path}`\n"
        
        report_content += "\n## 🔥 热门话题概览\n\n"
        
        # 添加话题概览
        for i, topic_result in enumerate(processed_topics[:10], 1):
            if 'error' not in topic_result:
                topic = topic_result['original_topic']
                keywords = topic_result.get('keywords', [])
                
                report_content += f"### {i}. {topic['merged_title']}\n\n"
                report_content += f"- **分类**: {topic['category']}\n"
                report_content += f"- **重要性**: {topic['importance_score']}/10\n"
                report_content += f"- **来源平台**: {', '.join(topic['source_platforms'])}\n"
                report_content += f"- **关键词**: {', '.join(keywords[:5])}\n"
                report_content += f"- **相关标题数**: {len(topic['source_titles'])}\n\n"
        
        if total_topics > 10:
            report_content += f"*... 还有 {total_topics - 10} 个话题，详见处理结果文件*\n\n"
        
        report_content += """## 🚀 下一步建议

1. **新闻检索**: 使用处理结果中的关键词和搜索查询词，从向量数据库中检索相关新闻
2. **内容生成**: 基于检索到的新闻内容，为每个话题生成深度分析文章
3. **质量评估**: 对生成的文章进行质量评估和人工审核
4. **发布分发**: 将优质文章发布到相应平台

## 📝 技术说明

本报告由新闻智能处理系统自动生成，整合了以下技术：
- 多平台新闻爬取
- LLM智能话题合并
- 深度话题分析
- 关键信息提取

---
*报告生成时间: {datetime.now().isoformat()}*
"""
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"📋 最终报告已生成: {report_file}")
            return str(report_file)
            
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")
            return None


def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 默认运行完整流程
        print("🚀 运行完整的新闻智能处理流程...")
        system = NewsIntelligenceSystem()
        results = system.run_full_pipeline()

    elif len(sys.argv) == 2:
        # 从格式化数据开始
        input_file = sys.argv[1]
        if not Path(input_file).exists():
            print(f"❌ 输入文件不存在: {input_file}")
            return

        print(f"🚀 从格式化数据开始处理: {input_file}")
        system = NewsIntelligenceSystem()
        results = system.run_from_formatted_data(input_file)

    else:
        print("📝 使用方法:")
        print(f"   python {sys.argv[0]}                    # 运行完整流程")
        print(f"   python {sys.argv[0]} <格式化文件.json>   # 从格式化数据开始")
        print("\n📖 示例:")
        print(f"   python {sys.argv[0]}")
        print(f"   python {sys.argv[0]} news_output/formatted_titles_20250707_193433.json")
        return

    try:

        # 显示最终结果
        print(f"\n📁 输出文件:")
        for key, file_path in results.items():
            if file_path:
                print(f"   {key}: {file_path}")

        print(f"\n✨ 处理完成！可以继续进行新闻检索和文章生成。")

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断处理")
    except Exception as e:
        print(f"\n❌ 系统错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
