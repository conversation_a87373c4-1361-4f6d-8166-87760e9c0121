#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻智能处理一条龙脚本
自动执行：爬取 -> 格式化 -> 合并话题 -> 深度分析 -> 生成报告
"""

import sys
import os
import json
from datetime import datetime
from news_crawler import NewsCrawler
from topic_merger import TopicMerger
from topic_processor import TopicProcessor


def run_full_pipeline():
    """运行完整的新闻处理流程"""
    print("🚀 启动新闻智能处理一条龙服务")
    print("=" * 60)
    
    try:
        # 第一步：爬取新闻
        print("\n📰 第一步：爬取热门新闻...")
        crawler = NewsCrawler()
        news_data = crawler.crawl_all()
        
        if not news_data:
            print("❌ 没有获取到新闻数据")
            return
        
        # 保存原始数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        raw_file = f"news_output/news_data_{timestamp}.json"
        os.makedirs("news_output", exist_ok=True)
        
        with open(raw_file, 'w', encoding='utf-8') as f:
            json.dump(news_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 爬取完成，获取 {sum(len(data) for data in news_data.values())} 条数据")
        
        # 第二步：格式化标题
        print("\n📝 第二步：格式化标题...")
        formatted_titles = []
        for platform, items in news_data.items():
            for item in items:
                formatted_titles.append({
                    'title': item.get('title', ''),
                    'platform': platform,
                    'url': item.get('url', ''),
                    'hot_value': item.get('hot_value', 0)
                })
        
        formatted_file = f"news_output/formatted_titles_{timestamp}.json"
        with open(formatted_file, 'w', encoding='utf-8') as f:
            json.dump(formatted_titles, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 格式化完成，处理 {len(formatted_titles)} 条标题")
        
        # 第三步：合并话题
        print("\n🔄 第三步：智能合并相似话题...")
        merger = TopicMerger()
        merged_topics = merger.merge_topics(formatted_titles)
        
        merged_file = f"news_output/merged_topics_{timestamp}.json"
        with open(merged_file, 'w', encoding='utf-8') as f:
            json.dump(merged_topics, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 话题合并完成，生成 {len(merged_topics)} 个合并话题")
        
        # 第四步：深度分析
        print("\n🧠 第四步：深度分析话题...")
        processor = TopicProcessor()
        processed_topics = processor.process_all_topics(merged_topics, use_concurrent=True)
        
        processed_file = f"news_output/processed_topics_{timestamp}.json"
        with open(processed_file, 'w', encoding='utf-8') as f:
            json.dump(processed_topics, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 深度分析完成，处理 {len(processed_topics)} 个话题")
        
        # 第五步：生成报告
        print("\n📊 第五步：生成智能报告...")
        report_file = f"news_output/intelligence_report_{timestamp}.md"
        generate_report(processed_topics, report_file)
        
        print(f"✅ 报告生成完成")
        
        # 完成
        print("\n" + "=" * 60)
        print("🎉 新闻智能处理完成！")
        print("=" * 60)
        print(f"📁 输出文件:")
        print(f"   原始数据: {raw_file}")
        print(f"   格式化标题: {formatted_file}")
        print(f"   合并话题: {merged_file}")
        print(f"   分析结果: {processed_file}")
        print(f"   智能报告: {report_file}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()


def run_from_file(file_path):
    """从现有文件开始处理"""
    print(f"🚀 从文件开始处理: {file_path}")
    print("=" * 60)
    
    try:
        # 读取数据
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 读取数据完成，共 {len(data)} 条")
        
        # 判断数据类型并处理
        if isinstance(data, list) and len(data) > 0:
            if 'title' in data[0]:
                # 格式化标题数据，从合并话题开始
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                print("\n🔄 开始合并话题...")
                merger = TopicMerger()
                merged_topics = merger.merge_topics(data)
                
                merged_file = f"news_output/merged_topics_{timestamp}.json"
                with open(merged_file, 'w', encoding='utf-8') as f:
                    json.dump(merged_topics, f, ensure_ascii=False, indent=2)
                
                print(f"✅ 话题合并完成，生成 {len(merged_topics)} 个合并话题")
                
                print("\n🧠 开始深度分析...")
                processor = TopicProcessor()
                processed_topics = processor.process_all_topics(merged_topics, use_concurrent=True)
                
                processed_file = f"news_output/processed_topics_{timestamp}.json"
                with open(processed_file, 'w', encoding='utf-8') as f:
                    json.dump(processed_topics, f, ensure_ascii=False, indent=2)
                
                print(f"✅ 深度分析完成，处理 {len(processed_topics)} 个话题")
                
                print("\n📊 生成智能报告...")
                report_file = f"news_output/intelligence_report_{timestamp}.md"
                generate_report(processed_topics, report_file)
                
                print("\n🎉 处理完成！")
                print(f"📁 输出文件:")
                print(f"   合并话题: {merged_file}")
                print(f"   分析结果: {processed_file}")
                print(f"   智能报告: {report_file}")
                
            elif 'merged_title' in data[0]:
                # 合并话题数据，从深度分析开始
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                print("\n🧠 开始深度分析...")
                processor = TopicProcessor()
                processed_topics = processor.process_all_topics(data, use_concurrent=True)
                
                processed_file = f"news_output/processed_topics_{timestamp}.json"
                with open(processed_file, 'w', encoding='utf-8') as f:
                    json.dump(processed_topics, f, ensure_ascii=False, indent=2)
                
                print(f"✅ 深度分析完成，处理 {len(processed_topics)} 个话题")
                
                print("\n📊 生成智能报告...")
                report_file = f"news_output/intelligence_report_{timestamp}.md"
                generate_report(processed_topics, report_file)
                
                print("\n🎉 处理完成！")
                print(f"📁 输出文件:")
                print(f"   分析结果: {processed_file}")
                print(f"   智能报告: {report_file}")
            else:
                print("❌ 无法识别的数据格式")
        else:
            print("❌ 数据格式错误")
            
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()


def generate_report(processed_topics, report_file):
    """生成智能报告"""
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 新闻智能分析报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"分析话题数: {len(processed_topics)}\n\n")
        f.write("---\n\n")
        
        for i, topic in enumerate(processed_topics, 1):
            if 'error' in topic:
                f.write(f"## {i}. 处理失败的话题\n\n")
                f.write(f"**错误**: {topic['error']}\n\n")
                continue
                
            original = topic.get('original_topic', {})
            f.write(f"## {i}. {original.get('merged_title', '未知话题')}\n\n")
            f.write(f"**分类**: {original.get('category', '未分类')}\n")
            f.write(f"**重要性**: {original.get('importance_score', 0)}/10\n")
            f.write(f"**来源平台**: {', '.join(original.get('source_platforms', []))}\n\n")
            
            if 'keywords' in topic:
                f.write(f"**关键词**: {', '.join(topic['keywords'])}\n\n")
            
            if 'background_context' in topic:
                f.write(f"**背景**: {topic['background_context']}\n\n")
            
            if 'key_points' in topic:
                f.write("**关键要点**:\n")
                for point in topic['key_points']:
                    f.write(f"- {point}\n")
                f.write("\n")
            
            if 'search_queries' in topic:
                f.write("**搜索建议**:\n")
                for query in topic['search_queries']:
                    f.write(f"- {query}\n")
                f.write("\n")
            
            f.write("---\n\n")


def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 无参数，运行完整流程
        run_full_pipeline()
    elif len(sys.argv) == 2:
        # 有文件参数，从文件开始处理
        file_path = sys.argv[1]
        if os.path.exists(file_path):
            run_from_file(file_path)
        else:
            print(f"❌ 文件不存在: {file_path}")
    else:
        print("❌ 参数错误")
        print("使用方法:")
        print("  python news_intelligence.py                    # 运行完整流程")
        print("  python news_intelligence.py <文件路径>          # 从文件开始处理")


if __name__ == "__main__":
    main()
