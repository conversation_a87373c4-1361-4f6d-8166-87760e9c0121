[{"merged_title": "88年了我们不曾忘不能忘不敢忘", "category": "时政", "source_titles": ["88年了我们不曾忘不能忘不敢忘", "七七事变88周年"], "source_platforms": ["weibo", "baidu"], "importance_score": 9}, {"merged_title": "国足vs韩国", "category": "体育", "source_titles": ["国足vs韩国", "国足0比3韩国"], "source_platforms": ["weibo", "weibo"], "importance_score": 8}, {"merged_title": "731部队常备40个活人用于实验", "category": "时政", "source_titles": ["731部队常备40个活人用于实验"], "source_platforms": ["baidu"], "importance_score": 8}, {"merged_title": "马斯克成立美国党及其影响", "category": "时政", "source_titles": ["马斯克成立美国党后会做什么", "马斯克的美国党能走多远", "美国党如何挑战驴象垄断", "特朗普说马斯克成立美国党荒谬"], "source_platforms": ["bilibili"], "importance_score": 8}, {"merged_title": "AI工具与应用", "category": "科技", "source_titles": ["公司开始利用 Ai 管理考核程序员的工作效率了", "想做一个英语面试练习的智能体", "键盘风暴+AI：一个 idea 制造机 2", "键盘风暴+AI：一个 idea 制造机", "摸鱼时间让 AI 做了一个冒险游戏", "ai coding 这样🔥，国内有类似 firebase supabase 这类产品吗，没找到啊", "AI coding + 开源 erp(odoo) 有没有搞头", "cursor agent 现在为什么会这么慢？", "我用 Cursor AI 两个周末做了一个代码片段管理工具", "检测 AI 与降 AI"], "source_platforms": ["v2ex"], "importance_score": 8}, {"merged_title": "老师因学生志愿未报清北解散群聊", "category": "教育", "source_titles": ["老师因学生志愿未报清北解散群聊", "教育局回应学生未报清北老师解散群聊"], "source_platforms": ["weibo", "weibo"], "importance_score": 7}, {"merged_title": "中方回应印度称中国借刀杀人", "category": "国际", "source_titles": ["中方回应印度称中国借刀杀人", "外交部回应“印度称中国借刀杀人”"], "source_platforms": ["weibo", "baidu"], "importance_score": 7}, {"merged_title": "中方回应美将与中方磋商收购TikTok", "category": "国际", "source_titles": ["中方回应美将与中方磋商收购TikTok"], "source_platforms": ["baidu"], "importance_score": 7}, {"merged_title": "F1赛事相关话题", "category": "体育", "source_titles": ["F1电影照进现实", "F1英国大奖赛正赛集锦", "比诺托的复仇", "F1电影剧情要走进现实了吗", "索伯时隔13年再登领奖台"], "source_platforms": ["bilibili"], "importance_score": 7}, {"merged_title": "台风与自然灾害", "category": "社会", "source_titles": ["济南暴雨", "美国得州严重洪灾", "强台风将巡游南方多省", "台风丹娜丝实时路径"], "source_platforms": ["bilibili"], "importance_score": 7}, {"merged_title": "国际时事", "category": "国际", "source_titles": ["世界周刊谈大而美法案之争", "探访阿富汗难民营", "世界周刊谈加沙的苦难"], "source_platforms": ["bilibili"], "importance_score": 7}, {"merged_title": "科技工具与开发", "category": "科技", "source_titles": ["分享一下我的新项目：智能域名搜索工具", "上线了一个自动起卦 AI 解卦工具，支持大衍筮法、梅花易数，还有 64 卦供易经爱好者学习", "一个用数据分析找到的小众木匠需求，我把它做成了个工具网站", "发布一个新的浏览器插件： DirsHunt Ext，快速帮你填写各类导航站表单", "快点 HAM 工具箱——业余无线电爱好者的全能助手！", "[自荐] AI Gist - 本地优先的 AI 提示词管理工具 | 开源免费", "Bytes Radar: Rust 远程代码仓库统计 CLI (Tokei 远程版)", "字节开源了 TraeAgent", "[自荐]开发了一个适用于朋友圈九宫格切图的小工具", "Gemini CLI 做的原生 macos 休息提醒 app", "学习机构费用太高，我做一个自考/成考/国开刷题的应用，有人想用吗？", "趣味开发：我变成世界首富！来看成为首富后的世界长啥样", "《书立》，一款富文本笔记软件，上线 Android 端，支持 MD 语法、嵌套表格、WebDAV，欢迎下载体验。", "新做的小工具 软著登记申请文件自动生成 免费使用", "用 .net 写了一个 gotify 客户端， Windows GUI 开发还是天下无敌", "用 DeepSeek 写了一个 B 站自律脚本", "周末写了一个免费 chrome 插件，可以把当前选项卡的链接转成 chromium 风格的二维码", "周末写了一个免费小工具，可以快速下载任意 APP Icon", "Prompt Security：你的剪贴板数据守护者", "我用 Cursor AI 两个周末做了一个代码片段管理工具", "推荐一个 Vibe Coding 工具： Claudia"], "source_platforms": ["v2ex"], "importance_score": 7}, {"merged_title": "警方通报42岁男子杭州南站坠楼", "category": "社会", "source_titles": ["警方通报42岁男子杭州南站坠楼", "杭州南站有人坠楼 工作人员回应"], "source_platforms": ["weibo", "baidu"], "importance_score": 6}, {"merged_title": "彩票中心通报女子中百万遭摊主夺票", "category": "社会", "source_titles": ["彩票中心通报女子中百万遭摊主夺票", "中百万大奖女子与摊主协商达成一致"], "source_platforms": ["weibo", "baidu"], "importance_score": 6}, {"merged_title": "中国央行连续第8个月增持黄金", "category": "财经", "source_titles": ["中国央行连续第8个月增持黄金"], "source_platforms": ["baidu"], "importance_score": 6}, {"merged_title": "苏超相关话题", "category": "体育", "source_titles": ["苏超爆火的底层逻辑", "苏超 主教练正在热身", "苏超隐藏MVP", "苏超 苏州逼宫南京"], "source_platforms": ["bilibili"], "importance_score": 6}, {"merged_title": "小米SU7相关话题", "category": "科技", "source_titles": ["小米YU7全网首提", "纽北老外如何评价SU7U"], "source_platforms": ["bilibili"], "importance_score": 6}, {"merged_title": "抗战历史与纪念", "category": "社会", "source_titles": ["88周年回望七七事变", "99岁抗战老兵讲述亲身经历", "震撼人心的经典抗战电影"], "source_platforms": ["bilibili"], "importance_score": 6}, {"merged_title": "电竞与游戏相关", "category": "娱乐", "source_titles": ["CFO教练喊话Tabe", "CFO vs AL数据前瞻", "鸣潮1级打赢675级狮像", "Bin赛后致歉"], "source_platforms": ["bilibili"], "importance_score": 6}, {"merged_title": "军事与国防", "category": "国际", "source_titles": ["直播卖房泄露军事机密", "第一视角参观山东舰"], "source_platforms": ["bilibili"], "importance_score": 6}, {"merged_title": "Angelababy巴黎时装周状态", "category": "娱乐", "source_titles": ["Angelababy巴黎时装周状态", "Angelababy眼尾贴钻", "Angelababy森林精灵"], "source_platforms": ["weibo", "weibo", "weibo"], "importance_score": 5}, {"merged_title": "陈梦回应退出世界排名", "category": "体育", "source_titles": ["陈梦正面回应退出世界排名", "陈梦回应退出世界排名：有舍才有得"], "source_platforms": ["weibo", "baidu"], "importance_score": 5}, {"merged_title": "幽门螺旋杆菌", "category": "健康", "source_titles": ["小英感染幽门螺旋杆菌", "幽门螺旋杆菌"], "source_platforms": ["weibo", "weibo"], "importance_score": 5}, {"merged_title": "B站内容创作者相关", "category": "娱乐", "source_titles": ["小城日常B站开播", "河南青作协副秘书长在B站写故事"], "source_platforms": ["bilibili"], "importance_score": 5}, {"merged_title": "娱乐与生活体验", "category": "娱乐", "source_titles": ["MrBeast挑战和猎豹比速度", "外卖大战的骑手是什么体验", "奥特曼的防卫队有多奇葩", "测谎仪是啥梗", "备万支箭实拍万箭齐发", "玩10小时乙游是什么体验", "39℃穿加绒连体衣出COS", "黄子韬抽象事件完整复盘", "灵笼2第9集PV解读"], "source_platforms": ["bilibili"], "importance_score": 5}, {"merged_title": "教育与求职", "category": "教育", "source_titles": ["学校不会教你的求职干货", "杨瀚森谈及NBA和CBA的区别"], "source_platforms": ["bilibili"], "importance_score": 5}, {"merged_title": "社会热点", "category": "社会", "source_titles": ["以法之名书记家宴现形计", "小资代表星巴克将何去何从"], "source_platforms": ["bilibili"], "importance_score": 5}, {"merged_title": "体育赛事", "category": "体育", "source_titles": ["常州队得分了", "逐帧分析常州队进球是否有效"], "source_platforms": ["bilibili"], "importance_score": 5}, {"merged_title": "魏晨回应结婚证是在潍坊领的", "category": "娱乐", "source_titles": ["魏晨回应结婚证是在潍坊领的", "魏晨称结婚证是在潍坊领的"], "source_platforms": ["weibo", "baidu"], "importance_score": 4}, {"merged_title": "黄宗泽说不结婚是为了气妈妈", "category": "娱乐", "source_titles": ["黄宗泽说不结婚是为了气妈妈", "黄宗泽不结婚是为了气妈妈"], "source_platforms": ["weibo", "baidu"], "importance_score": 4}, {"merged_title": "16个外甥来过暑假舅舅称背后没团队", "category": "社会", "source_titles": ["16个外甥来过暑假舅舅称背后没团队", "舅舅回应16个外甥连续5年来过暑假", "十几个外甥暑假到舅舅家每天吃8斤米"], "source_platforms": ["baidu", "baidu", "baidu"], "importance_score": 4}, {"merged_title": "小伙在山东舰甲板求婚", "category": "社会", "source_titles": ["小伙在山东舰甲板求婚"], "source_platforms": ["baidu"], "importance_score": 4}, {"merged_title": "DIY与创意", "category": "科技", "source_titles": ["自制140cm长的键盘"], "source_platforms": ["bilibili"], "importance_score": 4}, {"merged_title": "娱乐奖项", "category": "娱乐", "source_titles": ["宋佳白玉兰视后过誉了吗"], "source_platforms": ["bilibili"], "importance_score": 4}]