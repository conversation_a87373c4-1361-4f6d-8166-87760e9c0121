#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
话题处理模块
依次处理每个合并后的话题，为后续新闻检索和文章生成做准备
"""

import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from llm_client import LLMClient


class TopicProcessor:
    """话题处理器"""
    
    def __init__(self):
        """初始化话题处理器"""
        self.llm_client = LLMClient()
    
    def analyze_topic(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析单个话题，提取关键信息
        
        Args:
            topic: 合并后的话题信息
            
        Returns:
            分析结果，包含：
            - original_topic: 原始话题信息
            - keywords: 关键词列表
            - search_queries: 搜索查询词列表
            - background_context: 背景信息
            - key_points: 关键要点
            - related_entities: 相关实体（人物、机构、地点等）
        """
        print(f"🔍 分析话题: {topic['merged_title']}")
        
        # 构建系统提示词
        system_prompt = """你是一个专业的新闻分析师。你的任务是深入分析给定的热门话题，提取关键信息以便后续进行新闻检索和文章生成。

请按照以下要求分析话题：

1. **关键词提取**：
   - 提取3-8个最重要的关键词
   - 包括人物、事件、地点、机构等核心要素
   - 关键词要准确、具体，便于搜索

2. **搜索查询词**：
   - 生成5-10个不同角度的搜索查询词
   - 包括不同的表述方式和相关概念
   - 有助于全面检索相关新闻

3. **背景信息**：
   - 简要说明话题的背景和起因
   - 解释为什么这个话题会成为热点
   - 提供必要的上下文信息

4. **关键要点**：
   - 列出话题的3-6个关键要点
   - 包括主要事实、争议点、影响等
   - 要点要简洁明确

5. **相关实体**：
   - 识别相关的人物、机构、地点、产品等
   - 按类型分类整理
   - 有助于扩展搜索范围

请严格按照以下JSON格式输出：

```json
{
  "keywords": ["关键词1", "关键词2", "关键词3"],
  "search_queries": ["搜索词1", "搜索词2", "搜索词3"],
  "background_context": "话题背景信息",
  "key_points": ["要点1", "要点2", "要点3"],
  "related_entities": {
    "people": ["人物1", "人物2"],
    "organizations": ["机构1", "机构2"],
    "locations": ["地点1", "地点2"],
    "others": ["其他实体1", "其他实体2"]
  }
}
```"""

        # 构建用户提示词
        user_prompt = f"""请分析以下热门话题：

**话题标题**: {topic['merged_title']}
**话题分类**: {topic['category']}
**重要性评分**: {topic['importance_score']}/10
**来源平台**: {', '.join(topic['source_platforms'])}

**相关原始标题**:
{chr(10).join(f"- {title}" for title in topic['source_titles'])}

请深入分析这个话题，提取关键信息用于后续的新闻检索和文章生成。"""

        try:
            # 调用LLM
            response = self.llm_client.chat(
                prompt=user_prompt,
                system_prompt=system_prompt,
                max_tokens=2000,
                temperature=0.3
            )
            
            # 解析响应
            analysis = self._parse_analysis_response(response)
            
            if analysis:
                # 组合完整结果
                result = {
                    'original_topic': topic,
                    'analysis_time': datetime.now().isoformat(),
                    **analysis
                }
                
                print(f"✅ 话题分析完成: {len(analysis.get('keywords', []))} 个关键词")
                return result
            else:
                print("⚠️ 分析结果解析失败，使用备用方案")
                return self._fallback_analysis(topic)
                
        except Exception as e:
            print(f"❌ 话题分析失败: {e}")
            return self._fallback_analysis(topic)
    
    def _parse_analysis_response(self, response: str) -> Optional[Dict[str, Any]]:
        """解析LLM的分析响应"""
        try:
            # 尝试提取JSON部分
            import re
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 如果没有代码块，尝试直接解析
                json_str = response.strip()
            
            # 解析JSON
            result = json.loads(json_str)
            
            # 验证必要字段
            required_fields = ['keywords', 'search_queries', 'background_context', 'key_points', 'related_entities']
            if all(field in result for field in required_fields):
                return result
            
        except Exception as e:
            print(f"⚠️ 解析分析响应失败: {e}")
        
        return None
    
    def _fallback_analysis(self, topic: Dict[str, Any]) -> Dict[str, Any]:
        """备用分析方案"""
        print("🔄 执行备用分析方案...")
        
        # 从标题中提取简单关键词
        title = topic['merged_title']
        keywords = []
        
        # 简单的关键词提取（基于常见词汇）
        import re
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', title)
        keywords = [word for word in words if len(word) > 1][:5]
        
        return {
            'original_topic': topic,
            'analysis_time': datetime.now().isoformat(),
            'keywords': keywords,
            'search_queries': [title] + keywords,
            'background_context': f"关于'{title}'的热门话题",
            'key_points': [f"话题: {title}", f"分类: {topic['category']}"],
            'related_entities': {
                'people': [],
                'organizations': [],
                'locations': [],
                'others': []
            }
        }
    
    def process_all_topics(self, merged_topics: List[Dict[str, Any]],
                          use_concurrent: bool = True) -> List[Dict[str, Any]]:
        """
        处理所有合并后的话题

        Args:
            merged_topics: 合并后的话题列表
            use_concurrent: 是否使用并发处理

        Returns:
            处理结果列表
        """
        print(f"🚀 开始处理 {len(merged_topics)} 个话题...")
        print("=" * 60)

        if use_concurrent and len(merged_topics) > 1:
            # 使用并发处理
            return self._process_topics_concurrent(merged_topics)
        else:
            # 使用串行处理
            return self._process_topics_sequential(merged_topics)

    def _process_topics_concurrent(self, merged_topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """并发处理话题"""
        print(f"⚡ 使用并发模式处理话题...")

        # 准备并发请求
        requests = []
        for i, topic in enumerate(merged_topics):
            # 构建系统提示词
            system_prompt = """你是一个专业的新闻分析师。你的任务是深入分析给定的热门话题，提取关键信息以便后续进行新闻检索和文章生成。

请按照以下要求分析话题：

1. **关键词提取**：
   - 提取3-8个最重要的关键词
   - 包括人物、事件、地点、机构等核心要素
   - 关键词要准确、具体，便于搜索

2. **搜索查询词**：
   - 生成5-10个不同角度的搜索查询词
   - 包括不同的表述方式和相关概念
   - 有助于全面检索相关新闻

3. **背景信息**：
   - 简要说明话题的背景和起因
   - 解释为什么这个话题会成为热点
   - 提供必要的上下文信息

4. **关键要点**：
   - 列出话题的3-6个关键要点
   - 包括主要事实、争议点、影响等
   - 要点要简洁明确

5. **相关实体**：
   - 识别相关的人物、机构、地点、产品等
   - 按类型分类整理
   - 有助于扩展搜索范围

请严格按照以下JSON格式输出：

```json
{
  "keywords": ["关键词1", "关键词2", "关键词3"],
  "search_queries": ["搜索词1", "搜索词2", "搜索词3"],
  "background_context": "话题背景信息",
  "key_points": ["要点1", "要点2", "要点3"],
  "related_entities": {
    "people": ["人物1", "人物2"],
    "organizations": ["机构1", "机构2"],
    "locations": ["地点1", "地点2"],
    "others": ["其他实体1", "其他实体2"]
  }
}
```"""

            # 构建用户提示词
            user_prompt = f"""请分析以下热门话题：

**话题标题**: {topic['merged_title']}
**话题分类**: {topic['category']}
**重要性评分**: {topic['importance_score']}/10
**来源平台**: {', '.join(topic['source_platforms'])}

**相关原始标题**:
{chr(10).join(f"- {title}" for title in topic['source_titles'])}

请深入分析这个话题，提取关键信息用于后续的新闻检索和文章生成。"""

            requests.append({
                'prompt': user_prompt,
                'system_prompt': system_prompt,
                'max_tokens': 2000,
                'temperature': 0.3,
                'topic_index': i,
                'topic': topic
            })

        # 并发处理
        results = self.llm_client.batch_chat(requests)

        # 处理结果
        processed_topics = []
        for result in results:
            if result['success']:
                # 解析LLM响应
                analysis = self._parse_analysis_response(result['response'])
                if analysis:
                    # 组合完整结果
                    topic = requests[result['request_index']]['topic']
                    processed_result = {
                        'original_topic': topic,
                        'analysis_time': datetime.now().isoformat(),
                        **analysis
                    }
                    processed_topics.append(processed_result)
                    print(f"✅ 话题 {result['request_index'] + 1} 分析完成: {len(analysis.get('keywords', []))} 个关键词")
                else:
                    # 解析失败，使用备用方案
                    topic = requests[result['request_index']]['topic']
                    fallback_result = self._fallback_analysis(topic)
                    processed_topics.append(fallback_result)
                    print(f"⚠️ 话题 {result['request_index'] + 1} 解析失败，使用备用方案")
            else:
                # 请求失败
                topic = requests[result['request_index']]['topic']
                error_result = {
                    'original_topic': topic,
                    'error': result['error'],
                    'analysis_time': datetime.now().isoformat()
                }
                processed_topics.append(error_result)
                print(f"❌ 话题 {result['request_index'] + 1} 处理失败: {result['error']}")

        return processed_topics

    def _process_topics_sequential(self, merged_topics: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """串行处理话题"""
        print(f"🔄 使用串行模式处理话题...")

        processed_topics = []

        for i, topic in enumerate(merged_topics, 1):
            print(f"\n📋 处理进度: {i}/{len(merged_topics)}")

            try:
                # 分析话题
                analysis_result = self.analyze_topic(topic)
                processed_topics.append(analysis_result)

                # 显示简要结果
                keywords = analysis_result.get('keywords', [])
                print(f"🔑 关键词: {', '.join(keywords[:3])}{'...' if len(keywords) > 3 else ''}")

                # 请求间隔
                if i < len(merged_topics):
                    print(f"⏳ 等待 2 秒...")
                    time.sleep(2)

            except Exception as e:
                print(f"❌ 处理话题失败: {e}")
                # 添加错误记录
                processed_topics.append({
                    'original_topic': topic,
                    'error': str(e),
                    'analysis_time': datetime.now().isoformat()
                })

        print("\n" + "=" * 60)
        print(f"✅ 话题处理完成! 成功处理 {len(processed_topics)} 个话题")

        return processed_topics
    
    def save_processed_topics(self, processed_topics: List[Dict[str, Any]], 
                             output_file: str = None) -> str:
        """保存处理后的话题"""
        # 创建输出目录
        output_dir = Path("news_output")
        output_dir.mkdir(exist_ok=True)
        
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"processed_topics_{timestamp}.json"
        
        # 确保文件保存在输出目录中
        if not Path(output_file).is_absolute():
            output_file = output_dir / output_file
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(processed_topics, f, ensure_ascii=False, indent=2)
            
            print(f"💾 处理结果已保存到: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return None
    
    def print_processing_summary(self, processed_topics: List[Dict[str, Any]]):
        """打印处理结果摘要"""
        print("\n📋 话题处理结果摘要:")
        print("=" * 60)
        
        successful_count = 0
        error_count = 0
        
        for i, result in enumerate(processed_topics, 1):
            if 'error' in result:
                error_count += 1
                print(f"\n❌ 话题 {i}: {result['original_topic']['merged_title']}")
                print(f"   错误: {result['error']}")
            else:
                successful_count += 1
                topic = result['original_topic']
                print(f"\n✅ 话题 {i}: {topic['merged_title']}")
                print(f"   📂 分类: {topic['category']}")
                print(f"   🔑 关键词: {', '.join(result.get('keywords', [])[:5])}")
                print(f"   🔍 搜索词数: {len(result.get('search_queries', []))}")
                print(f"   📝 要点数: {len(result.get('key_points', []))}")
        
        print("\n" + "=" * 60)
        print(f"📊 处理统计:")
        print(f"   ✅ 成功: {successful_count} 个")
        print(f"   ❌ 失败: {error_count} 个")
        print(f"   📈 成功率: {successful_count/(successful_count+error_count)*100:.1f}%")


def main():
    """主函数 - 用于测试"""
    import sys
    
    if len(sys.argv) < 2:
        print("📝 使用方法:")
        print(f"   python {sys.argv[0]} <合并话题文件.json>")
        print("\n📖 示例:")
        print(f"   python {sys.argv[0]} news_output/merged_topics_20250707_193433.json")
        return
    
    input_file = sys.argv[1]
    
    try:
        # 加载合并话题数据
        with open(input_file, 'r', encoding='utf-8') as f:
            merged_topics = json.load(f)
        
        print(f"📂 加载合并话题: {input_file}")
        print(f"📊 话题数量: {len(merged_topics)}")
        
        # 创建处理器并执行处理（测试模式：只处理前3个话题）
        processor = TopicProcessor()
        test_topics = merged_topics[:3] if len(merged_topics) > 3 else merged_topics
        print(f"🧪 测试模式：只处理前 {len(test_topics)} 个话题")
        processed_topics = processor.process_all_topics(test_topics)
        
        # 保存结果
        output_file = processor.save_processed_topics(processed_topics)
        
        # 显示摘要
        processor.print_processing_summary(processed_topics)
        
        print(f"\n✨ 话题处理完成！结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")


if __name__ == "__main__":
    main()
