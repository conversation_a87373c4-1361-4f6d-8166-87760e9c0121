[{"merged_title": "88年了我们不曾忘不能忘不敢忘", "category": "社会", "source_titles": ["88年了我们不曾忘不能忘不敢忘", "先辈不屈不挠的抗争我们从未忘记", "日军每天生产2吨细菌用于轰炸中国"], "source_platforms": ["weibo"], "importance_score": 9}, {"merged_title": "国足vs韩国", "category": "体育", "source_titles": ["国足vs韩国", "东亚杯国足半场0比2韩国", "国足落后韩国"], "source_platforms": ["weibo"], "importance_score": 8}, {"merged_title": "中方回应印度称中国借刀杀人", "category": "国际", "source_titles": ["中方回应印度称中国借刀杀人", "中方已向印度提出交涉"], "source_platforms": ["weibo"], "importance_score": 8}, {"merged_title": "Angelababy巴黎时装周状态", "category": "娱乐", "source_titles": ["Angelababy巴黎时装周状态", "Angelababy眼尾贴钻"], "source_platforms": ["weibo"], "importance_score": 7}, {"merged_title": "罗马仕退款排到17万位", "category": "社会", "source_titles": ["罗马仕退款排到17万位", "罗马仕有员工仍希望正常上班"], "source_platforms": ["weibo"], "importance_score": 7}, {"merged_title": "幽门螺旋杆菌", "category": "健康", "source_titles": ["幽门螺旋杆菌", "小英感染幽门螺旋杆菌"], "source_platforms": ["weibo"], "importance_score": 6}, {"merged_title": "以法之名", "category": "社会", "source_titles": ["以法之名", "以法之名更新了"], "source_platforms": ["weibo"], "importance_score": 6}, {"merged_title": "彩票中心通报女子中百万遭摊主夺票", "category": "社会", "source_titles": ["彩票中心通报女子中百万遭摊主夺票"], "source_platforms": ["weibo"], "importance_score": 6}, {"merged_title": "中国一旅行团财物在意大利被洗劫", "category": "国际", "source_titles": ["中国一旅行团财物在意大利被洗劫"], "source_platforms": ["weibo"], "importance_score": 6}, {"merged_title": "老师因学生志愿未报清北解散群聊", "category": "教育", "source_titles": ["老师因学生志愿未报清北解散群聊"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "张子枫简直蜕变", "category": "娱乐", "source_titles": ["张子枫简直蜕变"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "黄宗泽说不结婚是为了气妈妈", "category": "娱乐", "source_titles": ["黄宗泽说不结婚是为了气妈妈"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "肖战玲娜贝儿打招呼", "category": "娱乐", "source_titles": ["肖战玲娜贝儿打招呼"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "虞书欣于适 我欲乘风", "category": "娱乐", "source_titles": ["虞书欣于适 我欲乘风"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "陈梦正面回应退出世界排名", "category": "体育", "source_titles": ["陈梦正面回应退出世界排名"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "周深双语歌曲归来致敬先烈", "category": "娱乐", "source_titles": ["周深双语歌曲归来致敬先烈"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "警方通报42岁男子杭州南站坠楼", "category": "社会", "source_titles": ["警方通报42岁男子杭州南站坠楼"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "丽江失联20岁女大学生遗体被找到", "category": "社会", "source_titles": ["丽江失联20岁女大学生遗体被找到"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "92岁老人病危兄妹在医院打起来了", "category": "社会", "source_titles": ["92岁老人病危兄妹在医院打起来了"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "血铅异常幼儿或为慢性铅中毒", "category": "健康", "source_titles": ["血铅异常幼儿或为慢性铅中毒"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "热射病的前兆", "category": "健康", "source_titles": ["热射病的前兆"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "27岁小伙颈部按摩后脑梗死", "category": "健康", "source_titles": ["27岁小伙颈部按摩后脑梗死"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "央视揭打造素人网红骗局", "category": "社会", "source_titles": ["央视揭打造素人网红骗局"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "官方回应举报者信息现举报对象办公室", "category": "社会", "source_titles": ["官方回应举报者信息现举报对象办公室"], "source_platforms": ["weibo"], "importance_score": 5}, {"merged_title": "中方回应美将对金砖国家加10%关税及收购TikTok磋商", "category": "国际", "source_titles": ["中方回应美将与中方磋商收购TikTok", "中方回应美要对金砖国家加10%关税", "中方回应如何应对美征收额外关税"], "source_platforms": ["baidu"], "importance_score": 9}, {"merged_title": "国防部驳斥'中方破坏阵风销售'及外交部回应'印度称中国借刀杀人'", "category": "时政", "source_titles": ["国防部驳斥'中方破坏阵风销售'", "外交部回应'印度称中国借刀杀人'"], "source_platforms": ["baidu"], "importance_score": 8}, {"merged_title": "中国旅游团在意大利被洗劫一空及丽江失联20岁女大学生确认身亡", "category": "社会", "source_titles": ["中国旅游团在意大利被洗劫一空", "丽江失联的20岁女大学生已确认身亡"], "source_platforms": ["baidu"], "importance_score": 7}, {"merged_title": "男子拒绝手术医生自掏3万也要救及血铅异常患儿家长借钱治病", "category": "社会", "source_titles": ["男子拒绝手术 医生自掏3万也要救", "血铅异常患儿家长：借钱也要治病"], "source_platforms": ["baidu"], "importance_score": 7}, {"merged_title": "香港市民送别山东舰编队场面震撼及七七事变88周年", "category": "时政", "source_titles": ["香港市民送别山东舰编队 场面震撼", "七七事变88周年", "731部队常备40个活人用于实验"], "source_platforms": ["baidu"], "importance_score": 7}, {"merged_title": "罗马仕退款排到17万位及业主欠费60万元头部物业公司不干了", "category": "社会", "source_titles": ["罗马仕退款排到17万位", "业主欠费60万元 头部物业公司不干了"], "source_platforms": ["baidu"], "importance_score": 6}, {"merged_title": "大学宿舍没空调引热议及烟台南山学院学生称每年学费上万", "category": "教育", "source_titles": ["#大学宿舍没空调会成为招生减章吗#", "烟台南山学院学生称每年学费上万"], "source_platforms": ["baidu"], "importance_score": 6}, {"merged_title": "16个外甥暑假住舅舅家每天吃8斤米及舅舅称背后没团队", "category": "社会", "source_titles": ["16个外甥来过暑假舅舅称背后没团队", "十几个外甥暑假到舅舅家每天吃8斤米"], "source_platforms": ["baidu"], "importance_score": 5}, {"merged_title": "陈梦相关新闻：妈妈偷户口本结婚及退出世界排名", "category": "娱乐", "source_titles": ["陈梦妈妈瞒着家人偷户口本结婚", "陈梦回应退出世界排名：有舍才有得"], "source_platforms": ["baidu"], "importance_score": 5}, {"merged_title": "杭州南站坠楼事件及警方通报", "category": "社会", "source_titles": ["警方通报42岁男子杭州南站坠楼", "杭州南站有人坠楼 工作人员回应"], "source_platforms": ["baidu"], "importance_score": 5}, {"merged_title": "台风暴雨天气相关提醒及事件", "category": "社会", "source_titles": ["遇上台风天 注意这9点", "一秒天黑 济南突降暴雨", "男子走出空调房竟瞬间'冷中暑'"], "source_platforms": ["baidu"], "importance_score": 5}, {"merged_title": "娱乐明星相关新闻合集", "category": "娱乐", "source_titles": ["罗家英患癌放弃化疗 妻子汪明荃回应", "朴宝剑晒与刘诗诗李庚希自拍", "黄宗泽不结婚是为了气妈妈", "李荣浩被常州热出双眼皮", "66岁倪萍回应整容传闻：就是老了", "58岁郑伊健被指体型发胖舞蹈无力"], "source_platforms": ["baidu"], "importance_score": 4}, {"merged_title": "体育赛事及相关新闻", "category": "体育", "source_titles": ["陪看东亚杯：中国vs韩国", "苏超开罚单：杨笑天因不文明动作被罚", "孙颖莎着急时 邱贻可及时'顺毛'"], "source_platforms": ["baidu"], "importance_score": 4}, {"merged_title": "科技产品及医疗相关新闻", "category": "科技", "source_titles": ["红米K90系列曝光：配骁龙强芯", "三黄片现金买18元刷医保26元", "中国央行连续第8个月增持黄金"], "source_platforms": ["baidu"], "importance_score": 4}, {"merged_title": "社会热点事件合集", "category": "社会", "source_titles": ["金毛疑餐馆蹭空调被打死主人发声", "横店短剧演员高强度工作去世？假", "中百万大奖女子与摊主协商达成一致", "女子起诉银行借1.12亿不还 一审重审", "高端奶粉卖不动了？飞鹤股价大跌", "内蒙古一景区载3人观光飞机坠地", "澳洲男生庆祝与女友同居 后空翻身亡", "考古梓渝都是笑料没黑料"], "source_platforms": ["baidu"], "importance_score": 3}, {"merged_title": "比特币市场动态与泡沫风险分析", "category": "财经", "source_titles": ["远古巨鲸转移 8 万枚比特币，溢价 130 万倍，比特币是否已经进入泡沫大顶？"], "source_platforms": ["zhihu"], "importance_score": 8}, {"merged_title": "演员雷佳音作品高产引发观众审美疲劳现象探讨", "category": "娱乐", "source_titles": ["演员雷佳音疑因作品高产，遭观众嫌弃「哪都有他」，一些观众为什么开始「烦」雷佳音了？"], "source_platforms": ["zhihu"], "importance_score": 6}, {"merged_title": "电子竞技赛事解说争议与职业素养讨论", "category": "体育", "source_titles": ["如何看待解说毛毛为季中赛 BLG 0:3 不敌 T1 解说台上破防喊出「游戏理解不在一个维度」而道歉？"], "source_platforms": ["zhihu"], "importance_score": 5}, {"merged_title": "飞行员就业市场变化原因分析", "category": "社会", "source_titles": ["几年前还是飞行员荒，为什么现在变成了飞行员过剩了呢？"], "source_platforms": ["zhihu"], "importance_score": 6}, {"merged_title": "直肠癌预防与早期发现健康指南", "category": "健康", "source_titles": ["知名旅游博主二师兄直肠癌晚期去世，年仅 38 岁，怎样能尽早发现直肠癌？生活中要注意些什么？"], "source_platforms": ["zhihu"], "importance_score": 7}, {"merged_title": "彩票中奖归属权法律争议案例分析", "category": "社会", "source_titles": ["安徽一顾客在刮刮乐摊位刮中 100 万大奖，摊主以顾客未支付票钱为由拒绝给中奖彩票，中奖彩票应该归谁？"], "source_platforms": ["zhihu"], "importance_score": 5}, {"merged_title": "小米产品设计与市场反应分析", "category": "科技", "source_titles": ["小米 YU7 深海蓝配色遇冷，为何惊艳设计却难入消费者法眼？", "小米路由器新版被指「偷偷减配」，关键零部件均被更换，对产品性能有什么影响？如何解读小米此举？"], "source_platforms": ["zhihu"], "importance_score": 6}, {"merged_title": "中国女篮战术安排与球员使用策略讨论", "category": "体育", "source_titles": ["中国女篮 63:76 败给了澳大利亚女篮，宫鲁鸣为什么雪藏了张子宇？出于什么考虑？"], "source_platforms": ["zhihu"], "importance_score": 6}, {"merged_title": "年轻人购房偏好变化与房产价值分析", "category": "社会", "source_titles": ["如何看待年轻人买房不关注投资价值，反而爱上楼层低、采光差的「树景房」？花几百万买「树景房」值吗？"], "source_platforms": ["zhihu"], "importance_score": 7}, {"merged_title": "特斯拉股价波动与马斯克政治动向影响分析", "category": "财经", "source_titles": ["特斯拉股价大跌、投资公司对 CEO 不满，马斯克建新党会对他的商业帝国带来多大冲击？"], "source_platforms": ["zhihu"], "importance_score": 8}, {"merged_title": "海水稻种植推广与国家粮食安全战略", "category": "农业", "source_titles": ["盐碱地种出的海水稻，口感难吃却越种越多，国家为何要大力发展？"], "source_platforms": ["zhihu"], "importance_score": 7}, {"merged_title": "新能源车自驾游趋势与川西旅游发展", "category": "旅游", "source_titles": ["暑期为什么越来越多人尝试新能源自驾川西？"], "source_platforms": ["zhihu"], "importance_score": 6}, {"merged_title": "特朗普关税政策与国际经贸关系影响", "category": "国际", "source_titles": ["如何看待特朗普向日本「开炮」威胁征新税？日美关税谈判陷入僵局对其他国家有哪些启示？"], "source_platforms": ["zhihu"], "importance_score": 8}, {"merged_title": "F-35战机采购削减对国防工业影响分析", "category": "军事", "source_titles": ["美国空军将 2026 财年 F-35 战机的采购数量削减一半，这将对洛马公司和国防工业产生怎样的影响？"], "source_platforms": ["zhihu"], "importance_score": 7}, {"merged_title": "未成年人犯罪与保险理赔法律问题探讨", "category": "法律", "source_titles": ["3 名初中生凌晨偷奔驰致严重车祸，保险公司以未成年人驾驶为由拒绝理赔，责任和赔偿问题如何处理？"], "source_platforms": ["zhihu"], "importance_score": 7}, {"merged_title": "夏季健康风险与冷饮食用注意事项", "category": "健康", "source_titles": ["上海女子称吃完冷饮后脑子抽筋了，医生诊断为「脑结冰」，脑结冰危害有多大？夏季吃冷饮有哪些注意事项？"], "source_platforms": ["zhihu"], "importance_score": 6}, {"merged_title": "微信市场垄断地位与替代软件发展困境", "category": "科技", "source_titles": ["为什么还没有软件能替代微信？"], "source_platforms": ["zhihu"], "importance_score": 7}, {"merged_title": "电子产品使用寿命与消费者使用习惯调查", "category": "科技", "source_titles": ["基础款 iPhone 能用几年？", "你的笔记本寿命是几年?"], "source_platforms": ["zhihu"], "importance_score": 6}, {"merged_title": "二次元文化商品高价现象与消费心理分析", "category": "文化", "source_titles": ["为什么各类二次元商品都能卖那么贵？而且还受到大多数人欢迎？"], "source_platforms": ["zhihu"], "importance_score": 5}, {"merged_title": "演员表演准备与人物小传重要性探讨", "category": "娱乐", "source_titles": ["郝蕾综艺内涵演员不写人物小传，她为什么这么重视人物小传？人物小传对于演员表演起到了什么样的作用？"], "source_platforms": ["zhihu"], "importance_score": 5}, {"merged_title": "金砖国家联合声明不点名批评美国及相关动态", "category": "国际", "source_titles": ["金砖国家联合声明不点名批评美国", "金砖里约热内卢宣言到底说了什么", "记者：印度是金砖峰会最大赢家", "印度为何没有阻挠金砖国家联合声明", "中方回应美称要对金砖国家加10%关税"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 9}, {"merged_title": "中国旅行团在意大利遭洗劫及国际安全事件", "category": "国际", "source_titles": ["中国一旅行团在意大利被洗劫一空", "俄军称遭乌克兰女性部队袭击", "以色列能否重创胡塞武装"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 8}, {"merged_title": "外交部回应国际事务及中印关系", "category": "时政", "source_titles": ["外交部：中方已向印度提出交涉", "外交部：支持印巴对话协商妥处分歧", "菲方要求中国销毁核武器？专家解读"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 8}, {"merged_title": "抗战纪念相关报道", "category": "社会", "source_titles": ["铭记这些浸染血泪的日子", "胖东来时代广场大屏播放抗日英雄谱", "纪念全民族抗战爆发88周年仪式举行", "88年前的卢沟桥到底发生了什么"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 7}, {"merged_title": "山东舰相关热点事件", "category": "军事", "source_titles": ["郭晶晶晒山东舰最佳打卡位", "小伙在山东舰甲板求婚"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 6}, {"merged_title": "特朗普与马斯克政治动态", "category": "国际", "source_titles": ["特朗普回应马斯克成立“美国党”", "评论员：马斯克成立美国党必然失败", "马斯克能当上美国总统吗", "马斯克为何对特朗普由爱转恨", "特朗普威胁“反美就加税”有何企图"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 7}, {"merged_title": "中国体育赛事及运动员动态", "category": "体育", "source_titles": ["国足vs韩国", "中国女篮再战澳大利亚能赢吗", "张子宇缺席热身赛是否为留底牌", "全红婵的伤会不会影响她的跳水生涯", "王励勤现场观战为孙颖莎加油"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 6}, {"merged_title": "社会热点事件及公共安全", "category": "社会", "source_titles": ["阿坝车辆坠崖事故第3个孩子仍未找到", "男子举报矿山瞒报 对方称给好处费", "官方通报女子中百万大奖遭摊主抢票", "SUV追尾前车 公交司机带头砸窗救人", "高温天老人晕倒街头 路人接力救助", "男子拒绝手术 医生自掏腰包也要救", "消防员在广西隆林抗洪牺牲？不实"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 7}, {"merged_title": "专家解读及评论分析", "category": "社会", "source_titles": ["专家：血铅异常幼儿或为慢性铅中毒", "专家回应为何长沙突然下大雨", "专家：冯德莱恩手中无牌可打", "评论员：美越协议救不了美国", "李大霄：中国股市或步入长胖牛", "A股缩量2199亿意味着什么", "有些通报为何和网民感受“温差”大"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 6}, {"merged_title": "娱乐明星动态", "category": "娱乐", "source_titles": ["粉丝落泪 鹿晗暖心安慰别哭啊"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 4}, {"merged_title": "地方体育及球员动态", "category": "体育", "source_titles": ["南京队球员：希望大家共同爱护苏超", "常州队球员：争取能进个球"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 4}, {"merged_title": "国际政治人物动态", "category": "国际", "source_titles": ["记者：杜特尔特罕见向海牙法官举白旗", "博主：尹锡悦的报应来了", "克罗地亚歌手带领50万人行纳粹礼"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 5}, {"merged_title": "法律与公共安全事件", "category": "社会", "source_titles": ["男子酒后启用“智驾”仍判醉驾", "郑州金水区人大常委会原主任薛燕被查"], "source_platforms": ["<PERSON><PERSON><PERSON>"], "importance_score": 5}, {"merged_title": "美国党与马斯克政治动向", "category": "时政", "source_titles": ["马斯克的美国党能走多远", "特朗普说马斯克成立美国党荒谬", "美国党如何挑战驴象垄断"], "source_platforms": ["bilibili"], "importance_score": 8}, {"merged_title": "F1赛事相关话题", "category": "体育", "source_titles": ["比诺托的复仇", "F1英国大奖赛正赛集锦", "F1电影剧情要走进现实了吗", "索伯时隔13年再登领奖台"], "source_platforms": ["bilibili"], "importance_score": 7}, {"merged_title": "台风灾害与极端天气", "category": "社会", "source_titles": ["台风丹娜丝实时路径", "强台风将巡游南方多省", "济南暴雨", "美国得州严重洪灾"], "source_platforms": ["bilibili"], "importance_score": 7}, {"merged_title": "苏超联赛相关话题", "category": "体育", "source_titles": ["苏超 主教练正在热身", "苏超 苏州逼宫南京", "苏超隐藏MVP", "常州队得分了", "逐帧分析常州队进球是否有效", "苏超第六轮10佳球"], "source_platforms": ["bilibili"], "importance_score": 6}, {"merged_title": "大而美法案及其影响", "category": "国际", "source_titles": ["世界周刊谈大而美法案之争", "大而美法案将如何影响美元"], "source_platforms": ["bilibili"], "importance_score": 6}, {"merged_title": "军事与国防相关话题", "category": "军事", "source_titles": ["直播卖房泄露军事机密", "第一视角参观山东舰"], "source_platforms": ["bilibili"], "importance_score": 6}, {"merged_title": "抗战历史与纪念", "category": "历史", "source_titles": ["99岁抗战老兵讲述亲身经历", "88周年回望七七事变", "震撼人心的经典抗战电影"], "source_platforms": ["bilibili"], "importance_score": 6}, {"merged_title": "电子竞技赛事", "category": "娱乐", "source_titles": ["CFO教练喊话Tabe", "CFO vs AL数据前瞻", "Bin赛后致歉"], "source_platforms": ["bilibili"], "importance_score": 5}, {"merged_title": "国际人道主义危机", "category": "国际", "source_titles": ["探访阿富汗难民营", "世界周刊谈加沙的苦难"], "source_platforms": ["bilibili"], "importance_score": 5}, {"merged_title": "汽车与科技产品评测", "category": "科技", "source_titles": ["纽北老外如何评价SU7U", "小米YU7全网首提"], "source_platforms": ["bilibili"], "importance_score": 5}, {"merged_title": "娱乐与生活体验", "category": "娱乐", "source_titles": ["外卖大战的骑手是什么体验", "玩10小时乙游是什么体验", "沉浸式宫廷宴值得去吗", "39℃穿加绒连体衣出COS"], "source_platforms": ["bilibili"], "importance_score": 4}, {"merged_title": "影视与动漫话题", "category": "娱乐", "source_titles": ["奥特曼的防卫队有多奇葩", "宋佳白玉兰视后过誉了吗"], "source_platforms": ["bilibili"], "importance_score": 4}, {"merged_title": "体育与篮球", "category": "体育", "source_titles": ["国足vs韩国陪看", "杨瀚森谈及NBA和CBA的区别"], "source_platforms": ["bilibili"], "importance_score": 4}, {"merged_title": "网络热点与梗", "category": "社会", "source_titles": ["测谎仪是啥梗", "黄子韬抽象事件完整复盘"], "source_platforms": ["bilibili"], "importance_score": 3}, {"merged_title": "创意与DIY", "category": "科技", "source_titles": ["备万支箭实拍万箭齐发", "自制140cm长的键盘"], "source_platforms": ["bilibili"], "importance_score": 3}, {"merged_title": "地方文化与生活", "category": "社会", "source_titles": ["河南青作协副秘书长在B站写故事", "小城日常B站开播"], "source_platforms": ["bilibili"], "importance_score": 3}, {"merged_title": "游戏挑战与体验", "category": "游戏", "source_titles": ["鸣潮1级打赢675级狮像", "MrBeast挑战和猎豹比速度"], "source_platforms": ["bilibili"], "importance_score": 3}, {"merged_title": "求职与教育", "category": "教育", "source_titles": ["学校不会教你的求职干货"], "source_platforms": ["bilibili"], "importance_score": 2}, {"merged_title": "商业与经济", "category": "经济", "source_titles": ["小资代表星巴克将何去何从"], "source_platforms": ["bilibili"], "importance_score": 2}, {"merged_title": "法律与社会", "category": "社会", "source_titles": ["以法之名书记家宴现形计"], "source_platforms": ["bilibili"], "importance_score": 2}, {"merged_title": "AI开发工具与应用合集：从智能体到代码管理", "category": "科技", "source_titles": ["想做一个英语面试练习的智能体", "键盘风暴+AI：一个 idea 制造机 2", "键盘风暴+AI：一个 idea 制造机", "ai coding 这样🔥，国内有类似 firebase supabase 这类产品吗，没找到啊", "AI coding + 开源 erp(odoo) 有没有搞头", "我用 Cursor AI 两个周末做了一个代码片段管理工具", "分享一篇关于智能体的学习思考总结小文", "如何产生一个想法？", "求教各位 v 友，我有两个项目，我想把他们两个的功能合并，怎么使用 ai 比较好"], "source_platforms": ["v2ex"], "importance_score": 8}, {"merged_title": "Claude相关工具与技术讨论", "category": "科技", "source_titles": ["AnyRouter， Claude code 中转站", "大佬们请教个问题，什么是 Claude code 子代理功能？", "如何防止 Claude 被封", "cursor agent 现在为什么会这么慢？"], "source_platforms": ["v2ex"], "importance_score": 7}, {"merged_title": "实用工具开发与分享", "category": "科技", "source_titles": ["一个用数据分析找到的小众木匠需求，我把它做成了个工具网站", "发布一个新的浏览器插件： DirsHunt Ext，快速帮你填写各类导航站表单", "快点 HAM 工具箱——业余无线电爱好者的全能助手！", "[自荐]开发了一个适用于朋友圈九宫格切图的小工具", "新做的小工具 软著登记申请文件自动生成 免费使用", "用 .net 写了一个 gotify 客户端， Windows GUI 开发还是天下无敌", "周末写了一个免费 chrome 插件，可以把当前选项卡的链接转成 chromium 风格的二维码", "周末写了一个免费小工具，可以快速下载任意 APP Icon", "Prompt Security：你的剪贴板数据守护者"], "source_platforms": ["v2ex"], "importance_score": 7}, {"merged_title": "AI与传统文化的结合：易经占卜工具", "category": "科技", "source_titles": ["上线了一个自动起卦 AI 解卦工具，支持大衍筮法、梅花易数，还有 64 卦供易经爱好者学习"], "source_platforms": ["v2ex"], "importance_score": 6}, {"merged_title": "编程工具与技术讨论", "category": "科技", "source_titles": ["v0.dev 效果感觉比 claude code max 的 opus 还要好用啊？", "Bytes Radar: Rust 远程代码仓库统计 CLI (Tokei 远程版)", "字节开源了 TraeAgent", "感觉我的 bun 安装依赖越来越慢了，有大佬知道什么原因吗", "推荐一个 Vibe Coding 工具： Claudia"], "source_platforms": ["v2ex"], "importance_score": 6}, {"merged_title": "AI娱乐应用与游戏开发", "category": "娱乐", "source_titles": ["摸鱼时间让 AI 做了一个冒险游戏", "趣味开发：我变成世界首富！来看成为首富后的世界长啥样", "关于情感反诈模拟器的游玩体验"], "source_platforms": ["v2ex"], "importance_score": 5}, {"merged_title": "实用生活技巧与工具", "category": "生活", "source_titles": ["抖音购物发现把要买的东西先加购物车，放几天就会有优惠券或者相同商品更低的价格的内容推送", "[免费] 用 Android 拦截 iPhone 的骚扰电话，解决苹果骚扰电话拦截不理想的问题", "onedrive 的正确用法？", "为啥 NAS 厂家都不设计硬盘单独开关", "发现一个有意思的天气网站（气温地图）", "发现永硕 E 盘还活着，且最复古的那个界面还有，满满的回忆感"], "source_platforms": ["v2ex"], "importance_score": 5}, {"merged_title": "教育学习工具开发", "category": "教育", "source_titles": ["学习机构费用太高，我做一个自考/成考/国开刷题的应用，有人想用吗？", "《书立》，一款富文本笔记软件，上线 Android 端，支持 MD 语法、嵌套表格、WebDAV，欢迎下载体验。"], "source_platforms": ["v2ex"], "importance_score": 5}, {"merged_title": "AI检测与安全讨论", "category": "科技", "source_titles": ["检测 AI 与降 AI", "分享一段和 ChatGPT 的对话，，，虽然我不认可这种 AI，但是它的回答还是挺好的。"], "source_platforms": ["v2ex"], "importance_score": 4}, {"merged_title": "硬件与技术问题讨论", "category": "科技", "source_titles": ["两个型号一样的 cm5 的板,emmc 却不一样大小,导致不能全盘对拷", "GV 保号提醒！我的 GV 号差点被回收，运气救了我。"], "source_platforms": ["v2ex"], "importance_score": 4}, {"merged_title": "开源项目与替代方案", "category": "科技", "source_titles": ["有哪些类似飞书多维表格的开源项目？", "[自荐] AI Gist - 本地优先的 AI 提示词管理工具 | 开源免费"], "source_platforms": ["v2ex"], "importance_score": 4}, {"merged_title": "个人职业发展分享", "category": "社会", "source_titles": ["芬兰 CS 硕士毕业，顺利找到工作"], "source_platforms": ["v2ex"], "importance_score": 3}, {"merged_title": "SEO工具与技术", "category": "科技", "source_titles": ["SEO 相关工具记录"], "source_platforms": ["v2ex"], "importance_score": 3}, {"merged_title": "特斯拉相关讨论", "category": "科技", "source_titles": ["观<看到有人无脑推荐特斯拉有感>有感"], "source_platforms": ["v2ex"], "importance_score": 3}, {"merged_title": "macOS工具开发", "category": "科技", "source_titles": ["Gemini CLI 做的原生 macos 休息提醒 app"], "source_platforms": ["v2ex"], "importance_score": 3}, {"merged_title": "B站相关工具开发", "category": "娱乐", "source_titles": ["用 DeepSeek 写了一个 B 站自律脚本"], "source_platforms": ["v2ex"], "importance_score": 2}]