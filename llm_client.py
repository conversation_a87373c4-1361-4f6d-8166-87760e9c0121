#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM客户端模块
提供统一的LLM API调用接口，支持多API key轮询和错误处理
"""

import json
import time
import random
import requests
import concurrent.futures
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
import threading
from config import get_llm_config


class LLMClient:
    """LLM客户端类，支持多API key轮询和并发控制"""
    
    def __init__(self):
        """初始化LLM客户端"""
        self.config = get_llm_config()
        self.api_keys = self.config.get("api_keys", [])
        self.base_url = self.config.get("base_url", "")
        self.model = self.config.get("model", "DeepSeek-V3-Fast")
        
        if not self.api_keys:
            raise ValueError("❌ 没有可用的API Key，请检查配置")
        
        # API key使用状态跟踪
        self.key_status = {i: {"available": True, "last_used": 0, "error_count": 0} 
                          for i in range(len(self.api_keys))}
        self.current_key_index = 0
        self.lock = threading.Lock()
        
        print(f"✅ LLM客户端初始化成功，加载了 {len(self.api_keys)} 个API Key")
    
    def _get_next_available_key(self) -> Optional[tuple]:
        """获取下一个可用的API key"""
        with self.lock:
            # 首先尝试找到可用的key
            for i in range(len(self.api_keys)):
                key_index = (self.current_key_index + i) % len(self.api_keys)
                status = self.key_status[key_index]
                
                # 检查key是否可用（错误次数少于3次，且距离上次使用超过1秒）
                current_time = time.time()
                if (status["available"] and 
                    status["error_count"] < 3 and 
                    current_time - status["last_used"] >= 1):
                    
                    # 更新使用状态
                    status["last_used"] = current_time
                    self.current_key_index = (key_index + 1) % len(self.api_keys)
                    
                    return key_index, self.api_keys[key_index]
            
            # 如果没有找到可用的key，重置所有key的错误计数
            for status in self.key_status.values():
                if status["error_count"] >= 3:
                    status["error_count"] = 0
                    status["available"] = True
            
            # 再次尝试获取key
            if self.key_status[0]["available"]:
                self.key_status[0]["last_used"] = time.time()
                return 0, self.api_keys[0]
            
            return None
    
    def _mark_key_error(self, key_index: int):
        """标记API key出现错误"""
        with self.lock:
            if key_index in self.key_status:
                self.key_status[key_index]["error_count"] += 1
                if self.key_status[key_index]["error_count"] >= 3:
                    self.key_status[key_index]["available"] = False
                    print(f"⚠️ API Key {key_index + 1} 暂时不可用（错误次数过多）")
    
    def _call_api(self, messages: List[Dict[str, str]], 
                  max_tokens: int = 2000, 
                  temperature: float = 0.7,
                  timeout: int = 60) -> Optional[str]:
        """调用LLM API"""
        key_info = self._get_next_available_key()
        if not key_info:
            raise Exception("❌ 没有可用的API Key")
        
        key_index, api_key = key_info
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        try:
            print(f"🔄 使用API Key {key_index + 1} 发送请求...")
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                if content:
                    print(f"✅ API Key {key_index + 1} 请求成功")
                    return content.strip()
                else:
                    raise Exception("API返回内容为空")
            
            elif response.status_code == 422:
                error_detail = response.json()
                if error_detail.get('status') == 20109:
                    print(f"💰 API Key {key_index + 1} 余额不足")
                    self._mark_key_error(key_index)
                raise Exception(f"API Key {key_index + 1} 错误: {error_detail}")
            
            else:
                raise Exception(f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"❌ API Key {key_index + 1} 请求失败: {e}")
            self._mark_key_error(key_index)
            raise e
    
    def chat(self, prompt: str, 
             system_prompt: str = None,
             max_tokens: int = 2000,
             temperature: float = 0.7,
             max_retries: int = 3) -> str:
        """
        发送聊天请求
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            max_tokens: 最大token数
            temperature: 温度参数
            max_retries: 最大重试次数
            
        Returns:
            LLM的回复内容
        """
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": prompt})
        
        last_exception = None
        for attempt in range(max_retries):
            try:
                result = self._call_api(messages, max_tokens, temperature)
                if result:
                    return result
                    
            except Exception as e:
                last_exception = e
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(0, 1)
                    print(f"⏳ 等待 {wait_time:.1f} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ 所有重试都失败了")
        
        if last_exception:
            raise last_exception
        else:
            raise Exception("未知错误：无法获取LLM响应")
    
    def get_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        with self.lock:
            available_keys = sum(1 for status in self.key_status.values() if status["available"])
            return {
                "total_keys": len(self.api_keys),
                "available_keys": available_keys,
                "model": self.model,
                "base_url": self.base_url,
                "key_status": dict(self.key_status)
            }

    def batch_chat(self, requests: List[Dict[str, Any]],
                   max_workers: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        并发处理多个聊天请求

        Args:
            requests: 请求列表，每个请求包含 prompt, system_prompt 等参数
            max_workers: 最大并发数，默认为可用API key数量

        Returns:
            结果列表，每个结果包含 success, response, error, request_index 等字段
        """
        if not requests:
            return []

        # 获取可用的API keys
        with self.lock:
            available_keys = [key for key, status in self.key_status.items() if status["available"]]

        # 设置最大并发数
        if max_workers is None:
            max_workers = min(len(available_keys), len(requests))
        else:
            max_workers = min(max_workers, len(available_keys), len(requests))

        print(f"🚀 开始并发处理 {len(requests)} 个请求，使用 {max_workers} 个并发线程")

        results = [None] * len(requests)  # 预分配结果列表

        # 确保有可用的API keys
        if not available_keys:
            print("❌ 没有可用的API keys")
            return [{'success': False, 'error': 'No available API keys', 'request_index': i} for i in range(len(requests))]

        def process_single_request(request_data):
            """处理单个请求"""
            index, request = request_data

            try:
                # 为每个线程分配专用的API key（轮询分配）
                thread_id = threading.current_thread().ident
                assigned_key_index = index % len(available_keys)
                assigned_key = available_keys[assigned_key_index]

                print(f"🔄 线程 {thread_id} 使用 API Key {assigned_key_index + 1} 处理请求 {index + 1}")

                # 调用API
                response = self._make_request_with_key(
                    assigned_key,
                    request.get('prompt', ''),
                    request.get('system_prompt', ''),
                    request.get('max_tokens', 2000),
                    request.get('temperature', 0.7)
                )

                return {
                    'success': True,
                    'response': response,
                    'error': None,
                    'request_index': index,
                    'thread_id': thread_id,
                    'api_key_index': assigned_key_index + 1
                }

            except Exception as e:
                return {
                    'success': False,
                    'response': None,
                    'error': str(e),
                    'request_index': index,
                    'thread_id': thread_id
                }

        # 使用线程池并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(process_single_request, (i, req)): i
                for i, req in enumerate(requests)
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_index):
                result = future.result()
                results[result['request_index']] = result

                # 显示进度
                completed = sum(1 for r in results if r is not None)
                if result['success']:
                    print(f"✅ 请求 {result['request_index'] + 1} 完成 (线程 {result['thread_id']}, API Key {result.get('api_key_index', '?')}) [{completed}/{len(requests)}]")
                else:
                    print(f"❌ 请求 {result['request_index'] + 1} 失败: {result['error']} [{completed}/{len(requests)}]")

        # 统计结果
        successful = sum(1 for r in results if r and r['success'])
        failed = len(results) - successful

        print(f"\n📊 并发处理完成:")
        print(f"   ✅ 成功: {successful}")
        print(f"   ❌ 失败: {failed}")
        print(f"   📈 成功率: {successful/len(results)*100:.1f}%")

        return results

    def _make_request_with_key(self, api_key: str, prompt: str,
                              system_prompt: str = "", max_tokens: int = 2000,
                              temperature: float = 0.7) -> str:
        """
        使用指定API key发送请求
        """
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        data = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        response = requests.post(
            self.base_url,
            headers=headers,
            json=data,
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            raise Exception(f"API请求失败: {response.status_code} - {response.text}")


def test_llm_client():
    """测试LLM客户端"""
    try:
        client = LLMClient()

        print("\n🧪 测试LLM客户端...")
        response = client.chat(
            prompt="你好，请简单介绍一下你自己。",
            system_prompt="你是一个有用的AI助手。",
            max_tokens=100
        )

        print(f"\n✅ 单个请求测试成功！")
        print(f"📝 LLM回复: {response[:100]}...")

        # 测试并发处理
        print(f"\n🚀 测试并发处理...")
        test_requests = [
            {
                'prompt': f"请用一句话介绍{topic}",
                'system_prompt': "你是一个简洁的AI助手。",
                'max_tokens': 50,
                'temperature': 0.5
            }
            for topic in ["人工智能", "区块链", "量子计算", "机器学习", "云计算"]
        ]

        batch_results = client.batch_chat(test_requests)

        print(f"\n📋 并发测试结果:")
        for i, result in enumerate(batch_results):
            if result['success']:
                print(f"   ✅ 请求 {i+1}: {result['response'][:50]}...")
            else:
                print(f"   ❌ 请求 {i+1}: {result['error']}")

        # 显示状态
        status = client.get_status()
        print(f"\n📊 客户端状态:")
        print(f"   - 总API Key数: {status['total_keys']}")
        print(f"   - 可用API Key数: {status['available_keys']}")
        print(f"   - 使用模型: {status['model']}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_llm_client()
