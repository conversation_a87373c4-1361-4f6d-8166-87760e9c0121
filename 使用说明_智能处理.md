# 新闻智能处理系统使用说明

## 🎯 系统功能

这个系统实现了你的新闻处理思路：

1. **✅ 已有功能**：从各类app热榜抓取热门话题
2. **🆕 新增功能**：将热门话题提供给LLM汇总合并相同类似的热门话题  
3. **🆕 新增功能**：依次向LLM输入合并后得出的每个话题，进行深度分析

## 🚀 使用方法

### 方法1：运行完整流程（推荐）

```bash
python news_intelligence.py
```

这会执行：
1. 爬取各平台热门话题
2. 格式化标题数据
3. LLM智能合并相似话题
4. 深度分析每个话题
5. 生成完整报告

### 方法2：从现有数据开始

```bash
python news_intelligence.py news_output/formatted_titles_20250707_193433.json
```

从已有的格式化标题文件开始处理。

### 方法3：单独运行各模块

```bash
# 1. 爬取新闻（你已经有了）
python news_crawler.py

# 2. 合并话题
python topic_merger.py news_output/formatted_titles_20250707_193433.json

# 3. 分析话题
python topic_processor.py news_output/merged_topics_20250707_194145.json
```

## 📁 输出文件

系统会在 `news_output/` 目录下生成：

- **原始数据**：`news_data_YYYYMMDD_HHMMSS.json`
- **格式化标题**：`formatted_titles_YYYYMMDD_HHMMSS.json`
- **合并话题**：`merged_topics_YYYYMMDD_HHMMSS.json`
- **分析结果**：`processed_topics_YYYYMMDD_HHMMSS.json`
- **最终报告**：`intelligence_report_YYYYMMDD_HHMMSS.md`

## 🔧 系统特点

### 智能话题合并
- **不限制数量**：根据话题实际相似性进行合并，不人为限制
- **多平台整合**：自动识别不同平台的相同话题
- **重要性评分**：根据影响力、关注度、时效性评分

### 深度话题分析
- **关键词提取**：提取3-8个核心关键词
- **搜索查询词**：生成5-10个不同角度的搜索词
- **背景信息**：分析话题背景和成因
- **关键要点**：列出3-6个关键要点
- **相关实体**：识别人物、机构、地点等

### 错误处理
- **多API Key轮换**：避免单点故障
- **自动重试机制**：网络错误自动重试
- **备用方案**：LLM失败时使用备用处理

## 📊 示例输出

### 合并话题示例
```json
{
  "merged_title": "七七事变88周年纪念活动",
  "category": "时政",
  "source_titles": [
    "88年了我们不曾忘不能忘不敢忘",
    "19370707永不能忘",
    "七七事变88周年"
  ],
  "source_platforms": ["微博热搜", "百度热搜", "今日头条"],
  "importance_score": 10
}
```

### 分析结果示例
```json
{
  "keywords": ["七七事变", "88周年", "纪念活动", "抗日战争"],
  "search_queries": [
    "七七事变88周年纪念",
    "卢沟桥事变纪念活动",
    "抗日战争纪念日"
  ],
  "background_context": "2025年7月7日是七七事变88周年...",
  "key_points": [
    "纪念抗日战争全面爆发88周年",
    "各地举行纪念活动",
    "铭记历史，勿忘国耻"
  ],
  "related_entities": {
    "people": ["抗日英烈"],
    "organizations": ["中国人民抗日战争纪念馆"],
    "locations": ["卢沟桥", "宛平城"],
    "others": ["七七事变", "抗日战争"]
  }
}
```

## 🔄 下一步

处理完成后，你可以：

1. **新闻检索**：使用分析结果中的关键词从向量数据库检索相关新闻
2. **文章生成**：基于检索到的新闻为每个话题生成深度文章
3. **质量评估**：对生成内容进行质量评估
4. **发布分发**：将优质内容发布到相应平台

## ⚡ 快速开始

```bash
# 直接运行，就这么简单！
python news_intelligence.py
```

系统会自动处理一切，无需复杂参数配置。
