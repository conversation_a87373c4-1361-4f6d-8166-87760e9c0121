#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
话题合并模块
使用LLM将各平台的热门话题进行智能合并和去重
"""

import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from llm_client import LLMClient


class TopicMerger:
    """话题合并器"""
    
    def __init__(self):
        """初始化话题合并器"""
        self.llm_client = LLMClient()
        self.platform_names = {
            'weibo': '微博热搜',
            'baidu': '百度热搜', 
            'zhihu': '知乎热榜',
            'toutiao': '今日头条',
            'bilibili': 'B站热搜',
            'github': 'GitHub趋势',
            'v2ex': 'V2EX热门',
            'ithome': 'IT之家'
        }
        
    def _prepare_topics_text(self, topics_data: Dict[str, List[str]]) -> str:
        """准备话题文本用于LLM处理"""
        text_parts = []
        total_count = 0
        
        for platform, titles in topics_data.items():
            if not titles:
                continue
                
            platform_name = self.platform_names.get(platform, platform.upper())
            text_parts.append(f"\n【{platform_name}】({len(titles)}条):")
            
            for i, title in enumerate(titles, 1):
                text_parts.append(f"{i:2d}. {title}")
            
            total_count += len(titles)
        
        header = f"以下是从{len(topics_data)}个平台收集到的{total_count}条热门话题："
        return header + "\n" + "\n".join(text_parts)
    
    def merge_topics(self, topics_data: Dict[str, List[str]], batch_size: int = 80, max_retries: int = 3) -> List[Dict[str, Any]]:
        """
        分批并发合并相似话题（增强版重试机制）

        Args:
            topics_data: 各平台的话题数据 {platform: [titles]}
            batch_size: 每批处理的话题数量
            max_retries: 每批最大重试次数

        Returns:
            合并后的话题列表
        """
        print("🔄 开始分批并发合并相似话题...")

        # 将所有话题平铺成列表，并记录原始信息
        all_topics = []
        original_count_by_platform = {}

        for platform, titles in topics_data.items():
            original_count_by_platform[platform] = len(titles)
            for title in titles:
                all_topics.append({
                    'title': title,
                    'platform': platform
                })

        total_topics = len(all_topics)
        total_batches = (total_topics + batch_size - 1) // batch_size
        print(f"📊 原始话题统计:")
        for platform, count in original_count_by_platform.items():
            print(f"   {platform}: {count} 个话题")
        print(f"📊 总共 {total_topics} 个话题，将分 {total_batches} 批处理（每批最多 {batch_size} 个）")

        # 分批处理
        batches = []
        for i in range(0, total_topics, batch_size):
            batch = all_topics[i:i + batch_size]
            batches.append(batch)

        # 构建系统提示词（强化JSON格式）
        system_prompt = """你是专业的新闻话题分析师。

**重要：必须严格按照JSON格式输出，不能有任何其他文字！**

任务：智能合并相似话题
- 相似主题合并，保留代表性标题
- 不同主题不强制合并
- 按重要性排序

分类：时政、娱乐、科技、体育、社会、国际、财经、教育、健康、其他
评分：1-10分（影响力、关注度、时效性）

**输出格式（严格遵守）**：
直接输出JSON数组，不要```json```包装，不要任何解释文字：

[
  {
    "merged_title": "合并后标题",
    "category": "分类",
    "source_titles": ["原标题1", "原标题2"],
    "source_platforms": ["平台1", "平台2"],
    "importance_score": 8
  }
]

**关键要求**：
1. 输出必须是有效的JSON格式
2. 不要添加任何解释文字
3. 不要用```json```包装
4. 确保所有字段都存在
5. importance_score必须是数字"""

        # 准备并发请求
        requests = []
        for i, batch in enumerate(batches):
            batch_text = self._prepare_batch_text(batch)
            user_prompt = f"""分析并合并以下 {len(batch)} 个话题：

{batch_text}

要求：
1. 按相似性智能合并
2. 按重要性排序
3. 直接输出JSON数组，不要任何其他文字
4. 不要用```json```包装
5. 确保JSON格式正确可解析

立即输出JSON："""

            requests.append({
                'prompt': user_prompt,
                'system_prompt': system_prompt,
                'max_tokens': 2000,
                'temperature': 0.3,
                'batch_index': i
            })

        # 增强重试机制处理
        return self._process_batches_with_retry(requests, batches, total_topics, max_retries, topics_data)

        except Exception as e:
            print(f"❌ 分批合并失败: {e}")
            print("🔄 使用备用合并方案...")
            return self._fallback_merge(topics_data)

    def _process_batches_with_retry(self, requests: List[Dict], batches: List[List], total_topics: int,
                                   max_retries: int, topics_data: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """
        增强重试机制处理批次
        """
        all_merged_topics = []
        failed_batches = []

        for retry_round in range(max_retries):
            if retry_round == 0:
                print(f"🚀 第 {retry_round + 1} 轮：并发处理 {len(requests)} 批话题...")
                current_requests = requests
                current_batches = batches
            else:
                print(f"🔄 第 {retry_round + 1} 轮：重试 {len(failed_batches)} 个失败批次...")
                current_requests = []
                current_batches = []
                for batch_info in failed_batches:
                    current_requests.append(batch_info['request'])
                    current_batches.append(batch_info['batch'])
                failed_batches = []

            if not current_requests:
                break

            # 并发处理当前批次
            results = self.llm_client.batch_chat(current_requests)

            # 处理结果
            successful_batches = 0
            for i, result in enumerate(results):
                batch_index = result.get('request_index', i)
                batch_data = current_batches[i] if i < len(current_batches) else []

                if result.get('success', False) and result.get('error') is None:
                    response = result.get('response', '')
                    print(f"🔍 批次 {batch_index} 原始回复: {response[:200]}...")

                    batch_topics = self._parse_llm_response(response)
                    if batch_topics:
                        all_merged_topics.extend(batch_topics)
                        successful_batches += 1
                        print(f"✅ 批次 {batch_index} 解析成功，得到 {len(batch_topics)} 个话题")

                        # 验证话题数量合理性
                        if len(batch_topics) > len(batch_data) * 2:
                            print(f"⚠️ 批次 {batch_index} 话题数量异常：输入 {len(batch_data)} 个，输出 {len(batch_topics)} 个")
                    else:
                        print(f"⚠️ 批次 {batch_index} 解析失败，加入重试队列")
                        failed_batches.append({
                            'request': current_requests[i],
                            'batch': batch_data,
                            'index': batch_index
                        })
                else:
                    print(f"❌ 批次 {batch_index} 处理失败: {result.get('error', 'Unknown error')}，加入重试队列")
                    failed_batches.append({
                        'request': current_requests[i],
                        'batch': batch_data,
                        'index': batch_index
                    })

            print(f"📊 第 {retry_round + 1} 轮结果：成功 {successful_batches}/{len(current_requests)} 批次")

            # 如果所有批次都成功，退出重试循环
            if not failed_batches:
                break

            # 如果还有重试机会，等待一下
            if retry_round < max_retries - 1 and failed_batches:
                import time
                wait_time = 2 ** retry_round
                print(f"⏳ 等待 {wait_time} 秒后进行下一轮重试...")
                time.sleep(wait_time)

        # 最终统计
        total_processed_topics = sum(len(batch['batch']) for batch in failed_batches) if failed_batches else 0
        total_successful_topics = total_topics - total_processed_topics

        print(f"📊 最终统计：")
        print(f"   原始话题: {total_topics} 个")
        print(f"   成功处理: {total_successful_topics} 个")
        print(f"   失败话题: {total_processed_topics} 个")
        print(f"   合并结果: {len(all_merged_topics)} 个话题")

        # 如果还有失败的批次，使用备用方案处理
        if failed_batches:
            print(f"⚠️ 仍有 {len(failed_batches)} 个批次失败，使用备用方案处理剩余话题...")
            failed_topics_data = {}
            for batch_info in failed_batches:
                for topic in batch_info['batch']:
                    platform = topic['platform']
                    if platform not in failed_topics_data:
                        failed_topics_data[platform] = []
                    failed_topics_data[platform].append(topic['title'])

            fallback_topics = self._fallback_merge(failed_topics_data)
            all_merged_topics.extend(fallback_topics)
            print(f"✅ 备用方案处理了 {len(fallback_topics)} 个话题")

        if not all_merged_topics:
            print("⚠️ 所有处理都失败，使用完整备用方案")
            return self._fallback_merge(topics_data)

        # 按重要性排序
        all_merged_topics.sort(key=lambda x: x.get('importance_score', 0), reverse=True)

        return all_merged_topics

    def _parse_llm_response(self, response: str) -> List[Dict[str, Any]]:
        """解析LLM的JSON响应（强化版）"""
        if not response or not response.strip():
            print("⚠️ 响应为空")
            return []

        try:
            response = response.strip()

            # 多种JSON提取方式
            json_str = None

            # 方式1：提取```json```包装的内容
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1).strip()

            # 方式2：提取```包装的内容
            elif '```' in response:
                json_match = re.search(r'```\s*(.*?)\s*```', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1).strip()

            # 方式3：查找JSON数组开始和结束
            elif '[' in response and ']' in response:
                start = response.find('[')
                end = response.rfind(']') + 1
                json_str = response[start:end].strip()

            # 方式4：直接尝试整个响应
            else:
                json_str = response

            if not json_str:
                print(f"⚠️ 无法提取JSON，原始响应: {response[:200]}...")
                return []

            # 解析JSON
            result = json.loads(json_str)
            
            # 验证格式
            if isinstance(result, list):
                validated_result = []
                for item in result:
                    if (isinstance(item, dict) and 
                        all(key in item for key in ['merged_title', 'category', 'source_titles', 'source_platforms', 'importance_score'])):
                        validated_result.append(item)
                
                return validated_result
            
        except Exception as e:
            print(f"⚠️ 解析LLM响应失败: {e}")
            print(f"📝 原始响应: {response[:500]}...")
        
        return []

    def _prepare_batch_text(self, batch: List[Dict[str, str]]) -> str:
        """准备批次文本"""
        text_parts = []
        for i, topic in enumerate(batch, 1):
            text_parts.append(f"{i}. [{topic['platform']}] {topic['title']}")
        return "\n".join(text_parts)

    def _fallback_merge(self, topics_data: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """备用合并方案：简单的基于关键词的合并"""
        print("🔄 执行备用合并方案...")
        
        all_topics = []
        for platform, titles in topics_data.items():
            platform_name = self.platform_names.get(platform, platform)
            for title in titles:
                all_topics.append({
                    'title': title,
                    'platform': platform_name,
                    'platform_key': platform
                })
        
        # 简单去重和分组
        merged_topics = []
        used_titles = set()
        
        for topic in all_topics:
            if topic['title'] not in used_titles:
                merged_topics.append({
                    'merged_title': topic['title'],
                    'category': '热点话题',
                    'source_titles': [topic['title']],
                    'source_platforms': [topic['platform']],
                    'importance_score': 5
                })
                used_titles.add(topic['title'])
        
        return merged_topics
    
    def save_merged_topics(self, merged_topics: List[Dict[str, Any]], 
                          output_file: str = None) -> str:
        """保存合并后的话题"""
        # 创建输出目录
        output_dir = Path("news_output")
        output_dir.mkdir(exist_ok=True)
        
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"merged_topics_{timestamp}.json"
        
        # 确保文件保存在输出目录中
        if not Path(output_file).is_absolute():
            output_file = output_dir / output_file
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(merged_topics, f, ensure_ascii=False, indent=2)
            
            print(f"💾 合并话题已保存到: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return None
    
    def print_merged_summary(self, merged_topics: List[Dict[str, Any]]):
        """打印合并结果摘要"""
        print("\n📋 话题合并结果摘要:")
        print("=" * 60)
        
        for i, topic in enumerate(merged_topics, 1):
            print(f"\n🔸 话题 {i}: {topic['merged_title']}")
            print(f"   📂 分类: {topic['category']}")
            print(f"   ⭐ 重要性: {topic['importance_score']}/10")
            print(f"   📱 来源平台: {', '.join(topic['source_platforms'])}")
            print(f"   📝 原始标题数: {len(topic['source_titles'])}")
            
            # 显示部分原始标题
            if len(topic['source_titles']) > 1:
                print(f"   📄 相关标题:")
                for j, title in enumerate(topic['source_titles'][:3], 1):
                    print(f"      {j}. {title}")
                if len(topic['source_titles']) > 3:
                    print(f"      ... 还有 {len(topic['source_titles']) - 3} 个相关标题")
        
        print("\n" + "=" * 60)
        print(f"📊 总计: {len(merged_topics)} 个合并话题")


def main():
    """主函数 - 用于测试"""
    import sys
    
    if len(sys.argv) < 2:
        print("📝 使用方法:")
        print(f"   python {sys.argv[0]} <格式化标题文件.json>")
        print("\n📖 示例:")
        print(f"   python {sys.argv[0]} news_output/formatted_titles_20250707_193433.json")
        return
    
    input_file = sys.argv[1]
    
    try:
        # 加载话题数据
        with open(input_file, 'r', encoding='utf-8') as f:
            topics_data = json.load(f)
        
        print(f"📂 加载话题数据: {input_file}")
        
        # 创建合并器并执行合并
        merger = TopicMerger()
        merged_topics = merger.merge_topics(topics_data)
        
        # 保存结果
        output_file = merger.save_merged_topics(merged_topics)
        
        # 显示摘要
        merger.print_merged_summary(merged_topics)
        
        print(f"\n✨ 话题合并完成！结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")


if __name__ == "__main__":
    main()
