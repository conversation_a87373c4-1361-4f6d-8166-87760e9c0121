#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
话题合并模块
使用LLM将各平台的热门话题进行智能合并和去重
"""

import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from llm_client import LLMClient


class TopicMerger:
    """话题合并器"""
    
    def __init__(self):
        """初始化话题合并器"""
        self.llm_client = LLMClient()
        self.platform_names = {
            'weibo': '微博热搜',
            'baidu': '百度热搜', 
            'zhihu': '知乎热榜',
            'toutiao': '今日头条',
            'bilibili': 'B站热搜',
            'github': 'GitHub趋势',
            'v2ex': 'V2EX热门',
            'ithome': 'IT之家'
        }
        
    def _prepare_topics_text(self, topics_data: Dict[str, List[str]]) -> str:
        """准备话题文本用于LLM处理"""
        text_parts = []
        total_count = 0
        
        for platform, titles in topics_data.items():
            if not titles:
                continue
                
            platform_name = self.platform_names.get(platform, platform.upper())
            text_parts.append(f"\n【{platform_name}】({len(titles)}条):")
            
            for i, title in enumerate(titles, 1):
                text_parts.append(f"{i:2d}. {title}")
            
            total_count += len(titles)
        
        header = f"以下是从{len(topics_data)}个平台收集到的{total_count}条热门话题："
        return header + "\n" + "\n".join(text_parts)
    
    def merge_topics(self, topics_data: Dict[str, List[str]], batch_size: int = 50) -> List[Dict[str, Any]]:
        """
        合并相似话题（分批并发处理）

        Args:
            topics_data: 各平台的话题数据 {platform: [titles]}
            batch_size: 每批处理的话题数量

        Returns:
            合并后的话题列表，每个话题包含：
            - merged_title: 合并后的标题
            - category: 话题分类
            - source_titles: 原始标题列表
            - source_platforms: 来源平台列表
            - importance_score: 重要性评分(1-10)
        """
        print("🔄 开始合并相似话题...")

        # 将所有话题平铺成列表
        all_topics = []
        for platform, titles in topics_data.items():
            for title in titles:
                all_topics.append({'title': title, 'platform': platform})

        print(f"📊 总共 {len(all_topics)} 条话题，将分批处理（每批 {batch_size} 条）")

        # 分批处理
        batches = [all_topics[i:i + batch_size] for i in range(0, len(all_topics), batch_size)]
        print(f"🔄 分为 {len(batches)} 批进行并发处理")

        return self._merge_topics_concurrent(batches)

    def _merge_topics_concurrent(self, batches: List[List[Dict[str, str]]]) -> List[Dict[str, Any]]:
        """并发处理话题合并"""
        # 构建系统提示词
        system_prompt = """你是一个专业的新闻话题分析师。你的任务是将一批热门话题进行智能合并和分类。

请按照以下要求处理：

1. **话题合并规则**：
   - 只合并真正相似、主题高度相关的话题
   - 不要为了减少数量而强行合并不相关的内容
   - 合并后的标题要简洁明确，不要用"及"连接不相关事件
   - 宁可保留更多独立话题，也不要混合不同主题
   - 如果两个话题完全不相关，就分别保留

2. **标题要求**：
   - 合并后的标题要简洁、聚焦
   - 避免冗长的复合标题
   - 选择最具代表性的核心表述

3. **分类要求**：
   - 为每个话题分配合适的分类（如：时政、娱乐、科技、体育、社会、国际等）

4. **重要性评分**：
   - 根据话题的社会影响力、关注度、时效性等因素评分（1-10分）

4. **输出格式**：
请严格按照以下JSON格式输出：

```json
[
  {
    "merged_title": "合并后的话题标题",
    "category": "话题分类",
    "source_titles": ["原始标题1", "原始标题2"],
    "source_platforms": ["平台1", "平台2"],
    "importance_score": 8
  }
]
```"""

        # 准备并发请求
        requests = []
        for i, batch in enumerate(batches):
            # 为每批构建话题文本
            batch_text = self._prepare_batch_text(batch)
            user_prompt = f"""请分析以下第{i+1}批热门话题并进行合并：

{batch_text}

请根据话题的实际相似性进行智能合并，按重要性评分从高到低排序。"""

            requests.append({
                'prompt': user_prompt,
                'system_prompt': system_prompt,
                'max_tokens': 2000,
                'temperature': 0.3
            })

        try:
            # 并发处理所有批次
            results = self.llm_client.batch_chat(requests)

            # 合并所有批次的结果
            all_merged_topics = []
            for i, result in enumerate(results):
                if 'error' in result and result['error'] is not None:
                    print(f"⚠️ 第{i+1}批处理失败: {result['error']}")
                    continue

                if 'response' not in result:
                    print(f"⚠️ 第{i+1}批没有响应数据")
                    continue

                batch_topics = self._parse_llm_response(result['response'])
                if batch_topics:
                    all_merged_topics.extend(batch_topics)
                    print(f"✅ 第{i+1}批合并完成，生成 {len(batch_topics)} 个话题")
                else:
                    print(f"⚠️ 第{i+1}批解析失败，响应内容: {result['response'][:200]}...")

            if not all_merged_topics:
                print("⚠️ 所有批次都失败，使用备用方案")
                # 重新构建原始格式用于备用方案
                topics_data = {}
                for batch in batches:
                    for item in batch:
                        platform = item['platform']
                        if platform not in topics_data:
                            topics_data[platform] = []
                        topics_data[platform].append(item['title'])
                return self._fallback_merge(topics_data)

            print(f"✅ 所有批次合并完成，总共生成 {len(all_merged_topics)} 个话题")
            return all_merged_topics

        except Exception as e:
            print(f"❌ 并发话题合并失败: {e}")
            print("🔄 使用备用合并方案...")
            # 重新构建原始格式用于备用方案
            topics_data = {}
            for batch in batches:
                for item in batch:
                    platform = item['platform']
                    if platform not in topics_data:
                        topics_data[platform] = []
                    topics_data[platform].append(item['title'])
            return self._fallback_merge(topics_data)

    def _prepare_batch_text(self, batch: List[Dict[str, str]]) -> str:
        """为一批话题准备文本"""
        platform_groups = {}
        for item in batch:
            platform = item['platform']
            if platform not in platform_groups:
                platform_groups[platform] = []
            platform_groups[platform].append(item['title'])

        text_parts = []
        for platform, titles in platform_groups.items():
            text_parts.append(f"【{platform}】({len(titles)}条):")
            for i, title in enumerate(titles, 1):
                text_parts.append(f"  {i}. {title}")

        return "\n".join(text_parts)
    
    def _parse_llm_response(self, response: str) -> List[Dict[str, Any]]:
        """解析LLM的JSON响应"""
        try:
            # 尝试提取JSON部分
            json_match = re.search(r'```json\s*(.*?)\s*```', response, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 如果没有代码块，尝试直接解析
                json_str = response.strip()
            
            # 解析JSON
            result = json.loads(json_str)
            
            # 验证格式
            if isinstance(result, list):
                validated_result = []
                for item in result:
                    if (isinstance(item, dict) and 
                        all(key in item for key in ['merged_title', 'category', 'source_titles', 'source_platforms', 'importance_score'])):
                        validated_result.append(item)
                
                return validated_result
            
        except Exception as e:
            print(f"⚠️ 解析LLM响应失败: {e}")
            print(f"📝 原始响应: {response[:500]}...")
        
        return []
    
    def _fallback_merge(self, topics_data: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """备用合并方案：简单的基于关键词的合并"""
        print("🔄 执行备用合并方案...")
        
        all_topics = []
        for platform, titles in topics_data.items():
            platform_name = self.platform_names.get(platform, platform)
            for title in titles:
                all_topics.append({
                    'title': title,
                    'platform': platform_name,
                    'platform_key': platform
                })
        
        # 简单去重和分组
        merged_topics = []
        used_titles = set()
        
        for topic in all_topics:
            if topic['title'] not in used_titles:
                merged_topics.append({
                    'merged_title': topic['title'],
                    'category': '热点话题',
                    'source_titles': [topic['title']],
                    'source_platforms': [topic['platform']],
                    'importance_score': 5
                })
                used_titles.add(topic['title'])
        
        return merged_topics
    
    def save_merged_topics(self, merged_topics: List[Dict[str, Any]], 
                          output_file: str = None) -> str:
        """保存合并后的话题"""
        # 创建输出目录
        output_dir = Path("news_output")
        output_dir.mkdir(exist_ok=True)
        
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"merged_topics_{timestamp}.json"
        
        # 确保文件保存在输出目录中
        if not Path(output_file).is_absolute():
            output_file = output_dir / output_file
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(merged_topics, f, ensure_ascii=False, indent=2)
            
            print(f"💾 合并话题已保存到: {output_file}")
            return str(output_file)
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return None
    
    def print_merged_summary(self, merged_topics: List[Dict[str, Any]]):
        """打印合并结果摘要"""
        print("\n📋 话题合并结果摘要:")
        print("=" * 60)
        
        for i, topic in enumerate(merged_topics, 1):
            print(f"\n🔸 话题 {i}: {topic['merged_title']}")
            print(f"   📂 分类: {topic['category']}")
            print(f"   ⭐ 重要性: {topic['importance_score']}/10")
            print(f"   📱 来源平台: {', '.join(topic['source_platforms'])}")
            print(f"   📝 原始标题数: {len(topic['source_titles'])}")
            
            # 显示部分原始标题
            if len(topic['source_titles']) > 1:
                print(f"   📄 相关标题:")
                for j, title in enumerate(topic['source_titles'][:3], 1):
                    print(f"      {j}. {title}")
                if len(topic['source_titles']) > 3:
                    print(f"      ... 还有 {len(topic['source_titles']) - 3} 个相关标题")
        
        print("\n" + "=" * 60)
        print(f"📊 总计: {len(merged_topics)} 个合并话题")


def main():
    """主函数 - 用于测试"""
    import sys
    
    if len(sys.argv) < 2:
        print("📝 使用方法:")
        print(f"   python {sys.argv[0]} <格式化标题文件.json>")
        print("\n📖 示例:")
        print(f"   python {sys.argv[0]} news_output/formatted_titles_20250707_193433.json")
        return
    
    input_file = sys.argv[1]
    
    try:
        # 加载话题数据
        with open(input_file, 'r', encoding='utf-8') as f:
            topics_data = json.load(f)
        
        print(f"📂 加载话题数据: {input_file}")
        
        # 创建合并器并执行合并
        merger = TopicMerger()
        merged_topics = merger.merge_topics(topics_data)
        
        # 保存结果
        output_file = merger.save_merged_topics(merged_topics)
        
        # 显示摘要
        merger.print_merged_summary(merged_topics)
        
        print(f"\n✨ 话题合并完成！结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")


if __name__ == "__main__":
    main()
